<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dh.message.mapper.MessageInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dh.message.entity.MessageInfo">
        <id column="id" property="id"/>
        <result column="msg_type" property="msgType"/>
        <result column="subject" property="subject"/>
        <result column="content" property="content"/>
        <result column="msg_url" property="msgUrl"/>
        <result column="business_id" property="businessId"/>
        <result column="business_type" property="businessType"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="create_date" property="createDate"/>
        <result column="receive_to" property="receiveTo"/>
        <result column="read_flag" property="readFlag"/>
        <result column="handle_flag" property="handleFlag"/>
        <result column="handle_date" property="handleDate"/>
        <result column="deadline_date" property="deadlineDate"/>
        <result column="params_value" property="paramsValue"/>
        <result column="message_template_id" property="messageTemplateId"/>
        <result column="message_template_param" property="messageTemplateParam"/>
        <result column="window_flag" property="windowFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, msg_type, subject, content, msg_url,business_id,business_type, create_by,create_by_name, create_date, receive_to, read_flag, handle_flag, handle_date,deadline_date,params_value,message_template_id,message_template_param,window_flag
    </sql>

    <select id="findMessageByCondition" resultType="com.dh.message.entity.MessageInfo">
        select * from message_info
        <where>
            <if test="subject != null and subject !=''">
                and subject like concat('%',#{subject},'%')
            </if>

            <choose>
                <when test="messageType == 1">
                    and msg_type = 1 and handle_flag = 0 and receive_to = #{userId}
                </when>
                <when test="messageType == 2">
                    and msg_type = 2 and receive_to = #{userId}
                </when>
                <when test="messageType == 3">
                    and msg_type = 3
                </when>
                <otherwise>
                    and msg_type = 1 and handle_flag = 1 and receive_to = #{userId}
                </otherwise>
            </choose>

        </where>
        order by create_date desc

    </select>

</mapper>
