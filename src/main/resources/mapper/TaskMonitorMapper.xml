<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dh.message.mapper.TaskMonitorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dh.message.bean.entity.TaskMonitor">
        <id column="id" property="id" />
        <result column="tms_id" property="tmsId" />
        <result column="business_type" property="businessType" />
        <result column="business_id" property="businessId" />
        <result column="business_ext_id" property="businessExtId" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="ext_params" property="extParams" typeHandler="com.dh.message.config.JsonTypeHandler"/>
        <result column="status" property="status" />
        <result column="related_uid" property="relatedUid" />
        <result column="project_manager_uid" property="projectManagerUid" />
        <result column="dept_manager_uid" property="deptManagerUid" />
        <result column="date_nodes" property="dateNodes" typeHandler="com.dh.message.config.EntityTypeHandler$LocalDateTimeMapTypeHandler" />
        <result column="alert_flag" property="alertFlag" />
        <result column="rate" property="rate" />
        <result column="del_flag" property="delFlag" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
    </resultMap>

    <resultMap id="taskMonitorMap" type="com.dh.message.dto.TaskMonitorInfoDTO" extends="BaseResultMap">
        <result column="alarm_levels" property="alarmLevels" typeHandler="com.dh.message.config.EntityTypeHandler$AlarmLevelConfigListTypeHandler" />
        <result column="notifications" property="notifications"
                typeHandler="com.dh.message.config.EntityTypeHandler$NotificationConfigListTypeHandler" />
        <result column="related_notification" property="relatedNotification" typeHandler="com.dh.message.config.EntityTypeHandler$RelatedNotificationConfigTypeHandler" />
        <result column="type" property="type"  />
        <result column="title" property="title"  />
    </resultMap>

    <resultMap id="taskMonitorVOMap" type="com.dh.message.bean.vo.TaskMonitorVO">
        <result column="id" property="id" />
        <result column="business_type" property="businessType" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="status" property="status" />
        <result column="task_content" property="taskContent" />
        <result column="create_date" property="createDate" />
        <result column="alarm_levels" property="alarmLevels" typeHandler="com.dh.message.config.EntityTypeHandler$AlarmLevelConfigListTypeHandler" />
        <result column="notifications" property="notifications"
                typeHandler="com.dh.message.config.EntityTypeHandler$NotificationConfigListTypeHandler" />
        <result column="type" property="type"  />
        <result column="title" property="title"  />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tms_id, business_type, business_id, business_ext_id, start_date, end_date, ext_params, status, related_uid, project_manager_uid, dept_manager_uid, date_nodes, alert_flag, del_flag, update_by, update_date, create_by, create_date
    </sql>

    <select id="getUnfinishedTask" resultMap="taskMonitorMap">
        SELECT
               TM.*,TMS.alarm_levels, TMS.notifications, TMS.type, TMS.title, TMS.related_notification
        FROM task_monitor TM
        LEFT JOIN task_monitor_setting TMS ON TMS.id=TM.tms_id
        WHERE TM.status=#{status} and TM.del_flag=0 <if test="tmId != null">and TM.id=#{tmId}</if>
    </select>

    <select id="getInProgressTaskByBatch" resultMap="taskMonitorMap">
        SELECT
            TM.*,TMS.alarm_levels, TMS.notifications, TMS.type, TMS.title
        FROM task_monitor TM
        LEFT JOIN task_monitor_setting TMS ON TMS.id=TM.tms_id
        WHERE TM.status in (1, 4) and TM.del_flag=0
          LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <select id="getInProgressTaskCount" resultType="long">
        SELECT COUNT(*)
        FROM task_monitor TM
        WHERE TM.status in (1, 4) and TM.del_flag=0
    </select>

    <select id="getTaskMonitorList" resultMap="taskMonitorVOMap">
        SELECT
            TM.*,TMS.alarm_levels, TMS.notifications, TMS.type, TMS.title
        FROM task_monitor TM
        LEFT JOIN task_monitor_setting TMS ON TMS.id=TM.tms_id
        WHERE TM.del_flag=0
        <if test="status != null">
            and TM.status=#{status}
        </if>
        <if test="searchDateBegin != null">
            and TM.start_date &gt;= #{searchDateBegin}
        </if>
        <if test="searchDateEnd != null">
            and TM.end_date &lt;= #{searchDateEnd}
        </if>
        <if test="userId !=null">
            and TM.related_uid like concat('%',#{userId},'%')
        </if>
        order by TM.create_date desc
    </select>

    <select id="getDeptHeadId" resultType="java.lang.Long">
        select distinct sd.head from sys_user su
        left join sys_dept sd on sd.id = su.dept_id
        where su.del_flag = 0 and sd.del_flag = 0
        and su.id in
        <foreach collection="userIdList" item="userId" open=" (" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="getRunningTaskMonitorList" resultMap="taskMonitorMap">
        select tm.*, tms.related_notification
        from task_monitor tm
        LEFT JOIN task_monitor_setting tms ON tms.id=tm.tms_id
        where tm.del_flag = 0 and tm.status in (1, 4)
        <if test="taskMonitorId != null">
            and tm.id = #{taskMonitorId}
        </if>
        <if test="businessId != null">
            and tm.business_id = #{businessId}
        </if>
        <if test="businessType != null and businessType != ''">
            and tm.business_type = #{businessType}
        </if>
        <if test="userId != null">
            and FIND_IN_SET(#{userId}, tm.related_uid) > 0
        </if>
    </select>

    <resultMap id="getLastRunningTaskMonitorMap" type="com.dh.dto.bean.dto.message.SubmitTaskMonitorResDTO">
        <id column="id" property="id" />
        <result column="tms_id" property="tmsId" />
        <result column="business_type" property="businessType" />
        <result column="business_id" property="businessId" />
        <result column="business_ext_id" property="businessExtId" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="ext_params" property="extParams" typeHandler="com.dh.message.config.JsonTypeHandler"/>
        <result column="status" property="status" />
        <result column="related_uid" property="relatedUid" />
        <result column="project_manager_uid" property="projectManagerUid" />
        <result column="dept_manager_uid" property="deptManagerUid" />
        <result column="date_nodes" property="dateNodes" typeHandler="com.dh.message.config.EntityTypeHandler$LocalDateTimeMapTypeHandler" />
        <result column="alert_flag" property="alertFlag" />
        <result column="del_flag" property="delFlag" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
    </resultMap>

    <select id="getLastRunningTaskMonitor" resultMap="getLastRunningTaskMonitorMap">
        select tm.*
        from task_monitor tm
        where tm.del_flag = 0 and tm.status != 3
        <if test="businessId != null">
            and tm.business_id = #{businessId}
        </if>
        <if test="businessType != null and businessType != ''">
            and tm.business_type = #{businessType}
        </if>
        <if test="userId != null">
            and FIND_IN_SET(#{userId}, tm.related_uid) > 0
        </if>
        order by create_date desc
        limit 1
    </select>
</mapper>
