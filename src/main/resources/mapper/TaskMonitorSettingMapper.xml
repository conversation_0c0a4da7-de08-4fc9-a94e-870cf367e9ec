<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dh.message.mapper.TaskMonitorSettingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dh.message.bean.entity.TaskMonitorSetting">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="type" property="type" />
        <result column="business_type" property="businessType" />
        <result column="create_by_name" property="createByName" />
        <result column="notifications" property="notifications" typeHandler="com.dh.message.config.EntityTypeHandler$NotificationConfigListTypeHandler" />
        <result column="related_notification" property="relatedNotification" typeHandler="com.dh.message.config.EntityTypeHandler$RelatedNotificationConfigTypeHandler" />
        <result column="alarm_levels" property="alarmLevels" typeHandler="com.dh.message.config.EntityTypeHandler$AlarmLevelConfigListTypeHandler" />
        <result column="del_flag" property="delFlag" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
        <result column="explain" property="explain" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, type, business_type, notifications, alarm_levels, del_flag, update_by, update_date, create_by, create_date
    </sql>

    <!-- 查询任务监控配置列表 -->
    <select id="getTaskMonitorSettingList" resultMap="BaseResultMap">
        SELECT *
        FROM task_monitor_setting
        <where>
            del_flag = 0
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="type != null">
                AND type = #{type}
            </if>
            <if test="businessType != null and businessType != ''">
                AND business_type LIKE CONCAT('%', #{businessType}, '%')
            </if>
            <if test="createByName != null and createByName != ''">
                AND create_by_name = #{createByName}
            </if>
            <if test="businessTypeList != null and businessTypeList.size >0">
                <foreach item="item" index="index" collection="businessTypeList" separator="," open="and business_type in (" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY create_date DESC
    </select>

    <select id="getTaskMonitorSettingDetail" resultMap="BaseResultMap">
        SELECT *
        FROM task_monitor_setting
        where del_flag = 0 and id = #{id}
    </select>

    <select id="getAllById" resultMap="BaseResultMap">
        SELECT *
        FROM task_monitor_setting
        where id = #{id}
    </select>

    <select id="getTaskMonitorSettingByBusinessType" resultMap="BaseResultMap">
        SELECT *
        FROM task_monitor_setting
        where del_flag = 0 and business_type = #{businessType}
    </select>
</mapper>
