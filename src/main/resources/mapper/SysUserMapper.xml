<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dh.message.mapper.SysUserMapper">

    <select id="getEmailsByUids" resultType="String">
        SELECT email
        FROM sys_user
        WHERE id IN
        <foreach item="uid" collection="uids" open="(" separator="," close=")">
            #{uid}
        </foreach>
    </select>

    <select id="getUserNameListById" resultType="java.lang.String">
        SELECT username
        FROM sys_user
        WHERE id IN
        <foreach item="id" collection="userIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>
