<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dh.message.mapper.TaskMonitorAlertMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dh.message.bean.entity.TaskMonitorAlert">
        <id column="id" property="id" />
        <result column="tm_id" property="tmId" />
        <result column="tms_id" property="tmsId" />
        <result column="alert_level" property="alertLevel" />
        <result column="message" property="message" />
        <result column="to_user_id" property="toUserId" />
        <result column="to_cc_users" property="toCcUsers" />
        <result column="is_warning" property="isWarning" />
        <result column="del_flag" property="delFlag" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tm_id, tms_id, alert_level, message, to_user_id, to_cc_users, is_warning, del_flag, update_by, update_date, create_by, create_date
    </sql>

</mapper>
