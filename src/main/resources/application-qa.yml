spring:
  datasource:
    druid:
      #基本属性 url、user、password
      #tinyInt1isBit=false MYSQL tinyint类型不转换为boolean类型 默认为true
      #&serverTimezone=GMT%2B8
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *********************************************************************************************************************************************************
      username: test01
      password: 1ybk#T9Y
      #配置初始化大小、最小、最大
      initial-size: 5
      min-idle: 10
      max-active: 20
      #配置获取连接等待超时的时间
      max-wait: 60000
      #配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 2000
      #配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 600000
      max-evictable-idle-time-millis: 900000
      validation-query: select 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      keep-alive: true
      phy-max-use-count: 100000
      #配置监控统计拦截的filters
      filter: stat
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-request-size: 100MB
      max-file-size: 20MB

#mybatis-plus 配置
mybatis-plus:
  #sql打印配置
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:/mapper/*.xml

logging:
  level:
    root: info
    com.dh.message: debug
  file:
    path: ./log

#系统配置
system-config:
  #消息最大推送次数
  max-send-num: 3
  #失败消息推送间隔时间 单位：分钟（失败次数*fail_send_time）
  fail-send-time: 10

#阿里云短信服务配置
aliyun-sms-config:
  default-connect-timeout: 10000
  default-read_timeout: 10000
  access_key_id: LTAI4FfhUd2ffNhnjZhAkrbo
  access_key_secret: ******************************
  sign_name: mySignName
#  template_code: myTemplateCode

#登录认证接口
auth:
  login-url: http://************/Auth/sys/user/info
  excluded-paths:
    - /actuator/*
    - /webjars/**
    - /v2/**
    - /swagger-resources/**
    - /csrf/**
    - /swagger-ui.html
    - /messageInfo/saveMessageInfo/**
    - /messageInfo/notifyMessage/**
    - /message/send
    - /messageInfo/notifyWorkflowMessage/**
    - /aliyunSms/**
    - /WxMessage/**
    - /event/**

dh:
  log:
    # 根据不同环境区分
    project: sys-operation-log-qa
    # 不记录日志的表名称
    excluded-table-names:
    # 不记录日志的接口路径
    excluded-paths:
