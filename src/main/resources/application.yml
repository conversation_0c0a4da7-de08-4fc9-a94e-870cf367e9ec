spring:
  application:
    name: message
  profiles:
    active: @spring.profiles.active@
  #国际化配置
  messages:
    basename: i18n.message
    encoding: utf-8
    cache-duration: PT30M
    use-code-as-default-message: true
  liquibase:
    enabled: true
    change-log: "classpath:/liquibase/db.changelog-master.xml"
    contexts: @spring.profiles.active@
#  freemarker:
#    enabled: true
#    charset: utf-8
#    check-template-location: true
#    template-loader-path: "classpath:/template/"
#    suffix: ftl
#  mail:
#    default-encoding: UTF-8
#    host: smtp.qiye.163.com
#    port: 994
#    protocol: smtp
#    username: <EMAIL>
#    password: haWhh9aj2019
#    properties:
#      mail:
#        smtp:
#          auth: true # 使用
#          starttls: # 使用 SSL 安全协议，须如下配置
#            enable: true
#            required: true
#          socketFactory:
#            port: 994
#            class: javax.net.ssl.SSLSocketFactory
#            fallback: false
#server
server:
  port: ${E_MESSAGE_PORT:8077}
  servlet:
    context-path: /message
    session:
      cookie:
        http-only: true
#system:
#  Robot: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=44a2dd9f-455c-4a5b-a997-450f670007ce  #获取群聊机器人的WebHook
#Spring boot 健康检查
management:
  endpoint:
    health:
      show-details: always
app:
  message:
    host: http://127.0.0.1:8077
xxl:
  job:
    admin:
      addresses: ${dh.dto.server.url}/xxl-job-admin
    executor:
      appname: ${spring.application.name}-executor-sample
      # 执行器端口号都以99开头
      port: 9965

# 应用相关配置
#app:
#  wxcp:
#    cor-pid: wweec4112a20068e11
#    corp-secret: 7lOq_Lf8hsEDELfiCxomN25VgJyX6pVeuNogU9AJouI
#    agent-id: 1000029

dh:
  log:
    endpoint: cn-shanghai-intranet.log.aliyuncs.com   # 内网访问
    accessKeyId: ${ACCESS_KEY_ID:}              #accessKeyId
    accessKeySecret: ${ACCESS_KEY_SECRET:}    #accessKeySecret
    logStore: uri-log  # 接口日志
    dmlLogStore: dml-log # dml日志（不填则不记录）
  framework:
    security:
      auth:
        # 不做登录认证的地址集合
        excluded-paths:
          - /actuator/*
          - /webjars/**
          - /v2/**
          - /swagger-resources/**
          - /csrf/**
          - /swagger-ui.html
          - /messageInfo/saveMessageInfo/**
          - /messageInfo/notifyMessage/**
          - /message/send
          - /messageInfo/notifyWorkflowMessage/**
          - /aliyunSms/**
          - /WxMessage/**
          - /event/**
