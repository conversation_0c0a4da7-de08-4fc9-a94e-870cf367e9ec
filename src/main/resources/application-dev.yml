spring:
  rabbitmq:
    host: ${DOCKER_RABBITMQ_HOST:*************}
    port: ${DOCKER_RABBITMQ_PORT:5672}
    username: ${DOCKER_RABBITMQ_USERNAME:dh_user}
    password: ${DOCKER_RABBITMQ_PASSWORD:dh2025@dev}
    virtual-host: ${DOCKER_RABBITMQ_VIRTUAL_HOST:/}
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************************************************
    username: platform_dev
    password: 6OOajQQp
    hikari:
      pool-name: ${spring.application.name}
      minimum-idle: 10
      idle-timeout: 600000
      maximum-pool-size: 30
      max-lifetime: 1800000
      auto-commit: true
      connection-timeout: 30000
      validation-timeout: 5000
      connection-test-query: SELECT 1
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 500
        prepStmtCacheSqlLimit: 2048
        useLocalSessionState: true
        rewriteBatchedStatements: true
        cacheResultSetMetadata: true
        cacheServerConfiguration: true
        elideSetAutoCommits: true
        maintainTimeStats: false
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-request-size: 100MB
      max-file-size: 20MB
  redis:
    port: 6379
    host: ************
    password: LGD
#mybatis-plus 配置
mybatis-plus:
#  #sql打印配置
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:/mapper/*.xml

logging:
  level:
    root: info
    com.dh.message: debug
    com.netflix.config: warn
    com.alibaba.druid: warn
    org.redisson: warn
    springfox.documentation.spring: warn
    com.ulisesbocchio: warn
    com.dh.framework: warn
    org.springframework.data: warn
    org.springframework.cloud: warn
    org.apache.catalina: warn
    org.springframework.scheduling: warn
    org.springframework.context: warn
    com.ulisesbocchio.jasyptspringboot: warn
  file:
    path: ./log

#系统配置
system-config:
  #消息最大推送次数
  max-send-num: 3
  #失败消息推送间隔时间 单位：分钟（失败次数*fail_send_time）
  fail-send-time: 10

#阿里云短信服务配置
aliyun-sms-config:
  default-connect-timeout: 10000
  default-read_timeout: 10000
  access_key_id: LTAI4FfhUd2ffNhnjZhAkrbo
  access_key_secret: ******************************
#  sign_name: 鼎衡智能管理通知
#  template_code: myTemplateCode

dh:
  log:
    # 根据不同环境区分
    project: sys-operation-log-dev
    # 不记录日志的表名称
    excluded-table-names:
    # 不记录日志的接口路径
    excluded-paths:
  dto:
    server:
      url: https://dev-api.dh-platform.com