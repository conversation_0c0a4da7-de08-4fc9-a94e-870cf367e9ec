--liquibase formatted sql

--changeset message_aliyun_sms_record:20200924-001
CREATE TABLE `message_aliyun_sms_record` (
  `id` varchar(30)  NOT NULL,
  `template_code` varchar(30)  DEFAULT NULL COMMENT '模板',
  `phone_numbers` varchar(1200)  DEFAULT NULL COMMENT '电话号码 多个 逗号分割',
  `return_code` varchar(100)  DEFAULT NULL COMMENT '返回code',
  `return_msg` varchar(100)  DEFAULT NULL COMMENT '返回msg',
  `return_requestid` varchar(100)  DEFAULT NULL COMMENT '返回requestid',
  `return_bizid` varchar(100)  DEFAULT NULL COMMENT '返回bizid',
  `params` varchar(1000)  DEFAULT NULL COMMENT '请求参数',
  `return_str` varchar(1000)  DEFAULT NULL COMMENT '返回全文',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='阿里云短信发送记录';


--changeset message_aliyun_sms_template:20200924-002
CREATE TABLE `message_aliyun_sms_template` (
  `template_code` varchar(30)  NOT NULL,
  `template_name` varchar(500)  DEFAULT NULL,
  `template_content` varchar(500)  DEFAULT NULL,
  `template_type` int(11) DEFAULT NULL,
  `params_json_str` varchar(500)  DEFAULT NULL,
  `params_count` int(11) DEFAULT NULL COMMENT '参数个数',
  `del_flag` int(11) DEFAULT NULL,
  `create_by` bigint(20) DEFAULT NULL,
  `create_date` datetime DEFAULT NULL,
  `update_by` bigint(20) DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  PRIMARY KEY (`template_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='阿里云短信模版信息';

--changeset message_channel:20200924-003
CREATE TABLE `message_channel` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `message_info_id` bigint(20) DEFAULT NULL COMMENT '消息主键id',
  `message_channel` int(11) DEFAULT NULL COMMENT '消息渠道（1 : PC 2 : APP 3 : 短信 4 : 邮件）',
  `channel_number` text COMMENT '渠道业务号码（例如：手机号，邮箱，微信openId）',
  `template_param` text COMMENT '发送模板参数(多个参数用 , 隔开)',
  `template_code` varchar(50) DEFAULT NULL COMMENT '发送模板编码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='消息渠道表';

--changeset message_info:************
CREATE TABLE `message_info` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `msg_type` int(11) DEFAULT NULL COMMENT '消息类型（1:我的待办，2:我的通知，3:公司公告）',
  `subject` varchar(100)  DEFAULT NULL COMMENT '消息主题',
  `content` varchar(1000) DEFAULT NULL COMMENT '消息内容',
  `msg_url` varchar(200)  DEFAULT NULL COMMENT '消息跳转链接',
  `business_id` varchar(50) DEFAULT NULL COMMENT '业务主键id,用于更新待办状态',
  `business_type` int(11) DEFAULT NULL COMMENT '业务类型（举例子1:初审，2：复审）',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人Id',
  `create_by_name` varchar(255)  DEFAULT NULL COMMENT '创建人姓名',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `receive_to` bigint(20) DEFAULT NULL COMMENT '接收人id',
  `read_flag` int(1) NOT NULL DEFAULT '0' COMMENT '是否已读（0：否，1：是）',
  `handle_flag` int(1) NOT NULL DEFAULT '0' COMMENT '是否处理（0：否，1：是）',
  `handle_date` datetime DEFAULT NULL COMMENT '处理时间',
  `deadline_date` varchar(11)  DEFAULT NULL COMMENT '处理截止时间（字符串）',
  `params_value` text COMMENT '参数集合(json格式)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='消息表';

--changeset message_push:************
CREATE TABLE `message_push` (
  `id` varchar(30)  NOT NULL,
  `msg_type` smallint(6) DEFAULT NULL COMMENT '消息类型',
  `send_channel` smallint(6) DEFAULT NULL COMMENT '推送渠道 1 APP 2 短信 3 邮件',
  `subject` varchar(100)  DEFAULT NULL COMMENT '消息主题',
  `content` varchar(1000)  DEFAULT NULL COMMENT '消息内容',
  `msg_url` varchar(200)  DEFAULT NULL COMMENT '消息跳转链接',
  `receive_to` varchar(30)  DEFAULT NULL COMMENT '消息接收人',
  `receive_to_num` varchar(100)  DEFAULT NULL COMMENT '消息接受人业务号码 例如：手机号码，微信openid等',
  `create_by` varchar(30)  DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `status` smallint(6) DEFAULT '1' COMMENT '0 无效  1有效',
  `send_status` smallint(6) DEFAULT '0' COMMENT '0 未推送 1推送成功 2 推送失败',
  `send_time` datetime DEFAULT NULL COMMENT '推送时间',
  `send_fail_times` smallint(6) DEFAULT '0' COMMENT '推送失败次数',
  `send_fail_msg` varchar(300)  DEFAULT NULL COMMENT '推送失败原因',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='消息推送表';


--changeset message_push_his:20200924-006
CREATE TABLE `message_push_his` (
  `id` varchar(30)  NOT NULL,
  `msg_type` smallint(6) DEFAULT NULL COMMENT '消息类型',
  `send_channel` smallint(6) DEFAULT NULL COMMENT '推送渠道',
  `subject` varchar(100)  DEFAULT NULL COMMENT '消息主题',
  `content` varchar(1000)  DEFAULT NULL COMMENT '消息内容',
  `msg_url` varchar(200)  DEFAULT NULL COMMENT '消息跳转链接',
  `receive_to` varchar(30)  DEFAULT NULL COMMENT '消息接收人',
  `receive_to_num` varchar(100)  DEFAULT NULL COMMENT '消息接受人业务号码 例如：手机号码，微信openid等',
  `create_by` varchar(30)  DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `status` smallint(6) DEFAULT '1' COMMENT '0 无效  1有效',
  `send_status` smallint(6) DEFAULT '0' COMMENT '0 未推送 1推送成功 2 推送失败',
  `send_time` datetime DEFAULT NULL COMMENT '推送时间',
  `send_fail_times` smallint(6) DEFAULT '0' COMMENT '推送失败次数',
  `send_fail_msg` varchar(300) DEFAULT NULL COMMENT '推送失败原因',
  `msg_archive_time` datetime DEFAULT NULL COMMENT '归档时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='消息推送归档表';

--changeset message_push_task_record:20200924-007
CREATE TABLE `message_push_task_record` (
  `id` varchar(30)  NOT NULL,
  `start_time` datetime DEFAULT NULL COMMENT '执行开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '执行结束时间',
  `type` int(11) DEFAULT NULL COMMENT '执行类型 1 首次推送 2 失败推送 3 失败数据处理',
  `record_num` int(11) DEFAULT NULL COMMENT '总记录数',
  `success_num` int(11) DEFAULT NULL COMMENT '成功记录数',
  `fail_num` int(11) DEFAULT NULL COMMENT '失败记录数',
  `create_time` datetime DEFAULT NULL COMMENT '保存时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='消息推送调度任务执行记录表';
