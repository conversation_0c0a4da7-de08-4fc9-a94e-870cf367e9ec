--liquibase formatted sql
--changeset task_monitor:************
CREATE TABLE `task_monitor` (
                                `id` bigint(20) unsigned NOT NULL,
                                `tms_id` bigint(11) unsigned NOT NULL COMMENT '任务监控配置ID',
                                `business_type` varchar(100) NOT NULL COMMENT '业务类型',
                                `business_id` bigint(20) unsigned NOT NULL COMMENT '业务ID',
                                `business_ext_id` bigint(20) unsigned DEFAULT '0' COMMENT '业务扩展ID',
                                `start_date` datetime DEFAULT NULL COMMENT '起始时间',
                                `end_date` datetime DEFAULT NULL COMMENT '结束时间',
                                `ext_params` json DEFAULT NULL COMMENT '扩展参数',
                                `status` int(10) unsigned NOT NULL COMMENT '任务状态(“未开始”、“进行中”、“已完成”、“已取消”、“已逾期”)',
                                `rate` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '任务完成进度',
                                `related_uid` varchar(255) DEFAULT NULL COMMENT '直接负责人id，多个以,分割',
                                `project_manager_uid` varchar(255) DEFAULT NULL COMMENT '项目主管id，多个以,分割',
                                `dept_manager_uid` bigint(20) unsigned DEFAULT NULL COMMENT '部门负责人',
                                `date_nodes` json DEFAULT NULL COMMENT '日期节点',
                                `alert_flag` int(20) DEFAULT '0' COMMENT '报警的触发状态',
                                `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标识(0: 否，1:是)',
                                `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
                                `update_date` datetime DEFAULT NULL COMMENT '更新时间',
                                `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
                                `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                `task_content` text COMMENT '任务内容',
                                PRIMARY KEY (`id`),
                                KEY `tms_id` (`tms_id`,`business_type`,`business_id`,`business_ext_id`),
                                KEY `start_date` (`start_date`,`end_date`,`status`,`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务监控表';

--changeset task_monitor:************
CREATE TABLE `task_monitor_alert` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                      `tm_id` bigint(20) unsigned DEFAULT NULL COMMENT '任务监控ID',
                                      `tms_id` bigint(20) unsigned DEFAULT NULL COMMENT '任务监控配置ID',
                                      `alert_level` int(11) unsigned DEFAULT NULL COMMENT '报警级别',
                                      `message` text COMMENT '报警信息',
                                      `to_user_id` varchar(255) DEFAULT NULL COMMENT '报警通知人',
                                      `to_cc_users` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '报警相关人',
                                      `is_warning` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否为预警报警',
                                      `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标识(0: 否，1:是)',
                                      `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
                                      `update_date` datetime DEFAULT NULL COMMENT '更新时间',
                                      `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
                                      `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                      PRIMARY KEY (`id`),
                                      KEY `tm_id` (`tm_id`,`tms_id`,`to_user_id`,`is_warning`)
) ENGINE=InnoDB AUTO_INCREMENT=3482 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务监控报警表';

--changeset task_monitor:20250731-003
CREATE TABLE `task_monitor_history` (
                                        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        `tm_id` bigint(20) unsigned DEFAULT NULL COMMENT '任务监控ID',
                                        `tms_id` bigint(20) DEFAULT NULL COMMENT '任务监控配置ID',
                                        `type` int(11) DEFAULT NULL COMMENT '更新的类型(1=>更新状态, 2=>延期任务, 3=>触发预警, 4=>触发报警)',
                                        `update_info` json DEFAULT NULL COMMENT '更新值(JSON)',
                                        `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标识(0: 否，1:是)',
                                        `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
                                        `update_date` datetime DEFAULT NULL COMMENT '更新时间',
                                        `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
                                        `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                        PRIMARY KEY (`id`),
                                        KEY `tm_id` (`tm_id`,`tms_id`,`type`,`create_date`)
) ENGINE=InnoDB AUTO_INCREMENT=7823 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务更新历史记录表';

--changeset task_monitor:************
CREATE TABLE `task_monitor_setting` (
                                        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                        `title` varchar(120) NOT NULL COMMENT '任务的标题',
                                        `type` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '任务的类型(1 => 分级任务, 2 => 简单任务, 3 => 完成度任务)',
                                        `business_type` varchar(100) NOT NULL DEFAULT '' COMMENT '业务类型',
                                        `notifications` json DEFAULT NULL COMMENT '通知配置',
                                        `alarm_levels` json DEFAULT NULL COMMENT '报警分级设置',
                                        `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标识(0: 否，1:是)',
                                        `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
                                        `update_date` datetime DEFAULT NULL COMMENT '更新时间',
                                        `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
                                        `create_date` datetime DEFAULT NULL COMMENT '创建时间',
                                        `create_by_name` varchar(100) DEFAULT NULL COMMENT '创建人姓名',
                                        `related_notification` json DEFAULT NULL COMMENT '责任人通知配置',
                                        `explain` json DEFAULT NULL COMMENT '说明',
                                        PRIMARY KEY (`id`),
                                        KEY `business_type` (`business_type`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务监控配置表';
