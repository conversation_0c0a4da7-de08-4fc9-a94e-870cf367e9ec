<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <!--
    1：includeAll 标签可以把一个文件夹下的所有 changelog 都加载进来。如果单个加载可以用 include。
    2：includeAll 标签里有两个属性：path 和 relativeToChangelogFile。
        2.1：path （在 include 标签里是 file）：指定要加载的文件或文件夹位置
        2.2：relativeToChangelogFile ：文件位置的路径是否相对于 root changelog 是相对路径，默认 false，即相对于 classpath 是相对路径。
        <includeAll path="liquibase/changelog/" relativeToChangelogFile="false"/>
    -->

<!--    <include file="classpath:liquibase/changelog/message_version_1.0.0.sql" relativeToChangelogFile="false"></include>-->
    <include file="classpath:liquibase/changelog/20250731001_add_table_task.mysql.sql" relativeToChangelogFile="false"></include>
    <includeAll path="changelog" relativeToChangelogFile="true"/>
</databaseChangeLog>
