package com.dh.message.feign;

import com.dh.common.util.R;
import com.dh.dto.bean.dto.message.GetLastRunningTaskMonitorDTO;
import com.dh.dto.bean.dto.message.SubmitTaskMonitorDTO;
import com.dh.dto.bean.dto.message.SubmitTaskMonitorResDTO;
import com.dh.dto.bean.dto.message.UpdateTaskMonitorDTO;
import com.dh.dto.feign.MessageFeignService;
import com.dh.message.service.TaskMonitorService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 任务监控Feign实现类
 *
 * @program: dh-all
 * @author: zgj
 * @create: 2025-07-24 10:12
 **/

@RestController
@Api(tags = "消息Feign")
@Slf4j
@RequestMapping("/feign")
public class MessageFeignServiceImpl implements MessageFeignService {

    @Resource
    private TaskMonitorService taskMonitorService;

    /**
     * 发起任务监控
     * @Author: zgj
     * @Date: 2023-06-08 20:45
     * @param submitTaskMonitorDTO 提交任务监控信息
     * @return: com.dh.common.util.R<java.lang.Long>
     **/
    @Override
    public R<SubmitTaskMonitorResDTO> submitTaskMonitor(SubmitTaskMonitorDTO submitTaskMonitorDTO) {
        return R.ok(taskMonitorService.submitTaskMonitor(submitTaskMonitorDTO));
    }

    /**
     * 更新任务监控进度
     * @Author: zgj
     * @Date: 2023-06-08 20:45
     * @param updateTaskMonitorDTO 入参
     * @return: com.dh.common.util.R<java.lang.Long>
     **/
    @Override
    public R<Long> updateTaskMonitorProgress(UpdateTaskMonitorDTO updateTaskMonitorDTO) {
        return R.ok(taskMonitorService.updateTaskMonitorProgress(updateTaskMonitorDTO));
    }

    /**
     * 获取最近一次进行中的任务监控数据
     * @Author: zgj
     * @Date: 2023-06-08 20:45
     * @param dto 入参
     * @return: com.dh.common.util.R<com.dh.dto.bean.dto.message.SubmitTaskMonitorResDTO>
     **/
    @Override
    public R<SubmitTaskMonitorResDTO> getLastRunningTaskMonitor(GetLastRunningTaskMonitorDTO dto) {
        return R.ok(taskMonitorService.getLastRunningTaskMonitor(dto));
    }

}
