package com.dh.message.enums;

import com.dh.dto.bean.dto.workflow.enums.ArrayValuable;
import lombok.Getter;

import java.util.Arrays;

@Getter
public enum TaskMonitorStatusEnum implements ArrayValuable<Integer> {
    DEFAULT(0, "初始化", "initialize"),

    STATUS_RUNNING(1, "进行中", "under way"),
    STATUS_FINISHED(2, "已完成", "Completed"),
    STATUS_CANCEL(3, "已取消", "Cancelled"),
    STATUS_OVERDUE(4, "已逾期", "Overdue"),
    STATUS_TERMINATE(5, "已终止", "Terminate");


    private final int code;
    private final String name;
    private final String nameEn;

    TaskMonitorStatusEnum(int code, String name, String nameEn) {
        this.code = code;
        this.name = name;
        this.nameEn = nameEn;
    }

    public static TaskMonitorStatusEnum fromCode(int code) {
        for (TaskMonitorStatusEnum status : TaskMonitorStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }

        throw new IllegalArgumentException("No enum constant with code " + code);
    }

    @Override
    public String toString() {
        return "{" + this.code + ", " + this.name + "}";
    }

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(TaskMonitorStatusEnum::getCode).toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}
