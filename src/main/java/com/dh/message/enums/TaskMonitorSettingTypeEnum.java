package com.dh.message.enums;

import com.dh.dto.bean.dto.workflow.enums.ArrayValuable;
import lombok.Getter;

import java.util.Arrays;

/**
 * 任务监控设置类型枚举
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Getter
public enum TaskMonitorSettingTypeEnum implements ArrayValuable<Integer> {
    // 分级任务
    LevelType(1, "分级任务"), // 简单任务
    SimpleType(2, "简单任务"),
    RateType(3, "进度任务");

    private final int code;
    private final String name;

    TaskMonitorSettingTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String toString() {
        return this.name;
    }

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(TaskMonitorSettingTypeEnum::getCode).toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}
