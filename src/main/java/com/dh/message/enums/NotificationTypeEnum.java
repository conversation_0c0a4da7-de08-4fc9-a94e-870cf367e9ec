package com.dh.message.enums;

import com.dh.dto.bean.dto.workflow.enums.ArrayValuable;
import lombok.Getter;

import java.util.Arrays;

/**
 * 通知类型
 *
 * <AUTHOR>
 * @since 2025/8/2
 */
@Getter
public enum NotificationTypeEnum implements ArrayValuable<String> {
    TYPE_EMAIL("email", "邮件", true),
    TYPE_TODO("todo", "代办"),
    TYPE_WECHAT("wechat", "企业微信"),
    TYPE_WECHAT_GROUP("wechat_group", "企业微信群", true);

    private final String title;
    private final String name;
    private final Boolean isGroup;

    NotificationTypeEnum(String name, String title, Boolean isGroup) {
        this.name = name;
        this.title = title;
        this.isGroup = isGroup;
    }

    NotificationTypeEnum(String name, String title) {
        this.name = name;
        this.title = title;
        this.isGroup = false;
    }

    public static NotificationTypeEnum fromCode(String name) {
        for (NotificationTypeEnum type : NotificationTypeEnum.values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }

        throw new IllegalArgumentException("No enum constant with name: " + name);
    }

    @Override
    public String toString() {
        return "{" + this.name + ", " + this.title + "}";
    }

    public static final String[] ARRAYS =
            Arrays.stream(values()).map(NotificationTypeEnum::getName).toArray(String[]::new);

    @Override
    public String[] array() {
        return ARRAYS;
    }
}
