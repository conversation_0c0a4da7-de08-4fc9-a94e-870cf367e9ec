package com.dh.message.enums;

import com.dh.dto.bean.dto.workflow.enums.ArrayValuable;
import lombok.Getter;

import java.util.Arrays;

@Getter
public enum TaskMonitorHistoryTypeEnum implements ArrayValuable<Integer> {
    // 分级任务
    UpdateStatusType(1, "更新状态"), // 简单任务
    DelayTaskType(2, "延期任务"),
    TriggerWarningType(3, "触发预警"),
    TriggerAlertType(4, "触发报警");

    private final int code;
    private final String name;

    TaskMonitorHistoryTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String toString() {
        return this.name;
    }

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(TaskMonitorHistoryTypeEnum::getCode).toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}
