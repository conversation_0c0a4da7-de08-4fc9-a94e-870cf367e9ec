package com.dh.message.bean.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class TaskMonitorSettingCardVO {

    /**
     * 任务的标题
     */
    @ApiModelProperty(value = "任务的标题")
    private String title;

    /**
     * 责任范围
     */
    @ApiModelProperty(value = "责任范围")
    private List<NotifyVO> liabilityList;


    /**
     * 系统预警和提醒
     */
    @ApiModelProperty(value = "系统预警和提醒")
    private List<MonitorRemindVO> notifyToList;

    /**
     * 逾期处理规则
     */
    @ApiModelProperty(value = "逾期处理规则(JSON)")
    private String explain;
}
