package com.dh.message.bean.vo;

import com.dh.message.bean.bo.AlarmLevelConfig;
import com.dh.message.bean.bo.NotificationConfig;
import com.dh.message.enums.TaskMonitorStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class TaskMonitorVO {
    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 起始时间
     */
    @ApiModelProperty(value = "起始时间")
    private LocalDateTime startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endDate;


    /**
     * 任务状态(“未开始”、“进行中”、“已完成”、“已取消”、“已逾期”)
     */
    @ApiModelProperty(value = "任务状态(“未开始”、“进行中”、“已完成”、“已取消”、“已逾期”)")
    private Integer status;

    /**
     * 任务内容
     */
    @ApiModelProperty(value = "任务内容")
    private String taskContent;

    /**
     * 任务报警配置标题
     */
    @ApiModelProperty(value = "任务报警配置标题")
    private String title;

    /**
     * 任务报警配置标题
     */
    @ApiModelProperty(value = "任务报警配置标题")
    private Integer type;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    /**
     * 报警分级配置列表
     */
    @ApiModelProperty(value = "报警分级配置列表")
    private List<AlarmLevelConfig> alarmLevels;

    /**
     * 通知配置列表
     */
    @ApiModelProperty(value = "通知配置列表")
    private List<NotificationConfig> notifications;

    public String getStatusStr() {
        return TaskMonitorStatusEnum.fromCode(status).getName()!=null?TaskMonitorStatusEnum.fromCode(status).getName():"";
    }

}
