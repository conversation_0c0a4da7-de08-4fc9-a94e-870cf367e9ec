package com.dh.message.bean.vo;

import com.dh.message.bean.bo.AlarmLevelConfig;
import com.dh.message.bean.bo.NotificationConfig;
import com.dh.message.bean.bo.RelatedNotificationConfig;
import com.dh.message.config.CollectionNullSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 任务监控配置表返回前端对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "TaskMonitorSettingVO对象", description = "任务监控配置表返回前端对象")
public class TaskMonitorSettingVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 任务的标题
     */
    @ApiModelProperty(value = "任务的标题")
    private String title;

    /**
     * 任务的类型(1 => 分级任务, 2 => 简单任务)
     */
    @ApiModelProperty(value = "任务的类型(1 => 分级任务, 2 => 简单任务)")
    private Integer type;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 说明
     */
    @ApiModelProperty(value = "说明")
    private String explain;

    /**
     * 通知配置
     */
    @ApiModelProperty(value = "通知配置")
    @JsonSerialize(nullsUsing = CollectionNullSerialize.class)
    private List<NotificationConfig> notifications;

    /**
     * 责任人通知配置
     */
    @ApiModelProperty(value = "责任人通知配置")
    private RelatedNotificationConfig relatedNotification;

    /**
     * 报警分级设置
     */
    @ApiModelProperty(value = "报警分级设置")
    @JsonSerialize(nullsUsing = CollectionNullSerialize.class)
    private List<AlarmLevelConfig> alarmLevels;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    /**
     * 更新人ID
     */
    @ApiModelProperty(value = "更新人ID")
    private Long updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateDate;
}
