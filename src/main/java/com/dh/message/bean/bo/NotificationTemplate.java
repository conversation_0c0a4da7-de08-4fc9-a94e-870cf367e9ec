package com.dh.message.bean.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.PropertyPlaceholderHelper;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 通知模板对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "NotificationTemplate对象", description = "通知模板对象")
public class NotificationTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报警级别
     */
    @ApiModelProperty(value = "报警级别")
    private Integer level;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    private String message;

    /**
     * 预警消息内容
     */
    @ApiModelProperty(value = "预警消息内容")
    private String preMessage;
}
