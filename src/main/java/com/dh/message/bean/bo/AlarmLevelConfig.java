package com.dh.message.bean.bo;

import com.dh.message.enums.LevelUnitEnum;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 报警分级配置对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "AlarmLevelConfig对象", description = "报警分级配置对象")
public class AlarmLevelConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 天数偏移
     */
    @ApiModelProperty(value = "天数偏移")
    private Integer day;

    /**
     * 报警进度阈值(预留)
     */
    @ApiModelProperty(value = "报警进度阈值")
    private Double rate;

    /**
     * 单位（DAYS，HOURS）
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 报警名称
     */
    @ApiModelProperty(value = "报警名称")
    private String name;

    /**
     * 报警级别
     */
    @ApiModelProperty(value = "报警级别")
    private Integer level;

    /**
     * 预警规则
     */
    @ApiModelProperty(value = "预警规则")
    private String ruleExplain;

    /**
     * 队列列表
     */
    @ApiModelProperty(value = "队列列表")
    private String queues;

    /**
     * 通知对象列表
     */
    @ApiModelProperty(value = "通知对象列表")
    @JsonDeserialize(contentAs = NotifyTarget.class)
    private List<NotifyTarget> notifyTo;

    public String getUnit() {
        if (unit == null) {
            unit = LevelUnitEnum.DAYS.getName();
        }
        return unit;
    }
}
