package com.dh.message.bean.bo;

import com.dh.message.bean.bo.NotificationTemplate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 通知配置对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "NotificationConfig对象", description = "通知配置对象")
public class NotificationConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 通知类型
     */
    @ApiModelProperty(value = "通知类型")
    private String type;

    /**
     * 通知参数
     */
    @ApiModelProperty(value = "通知参数")
    private Map<String, Object> params;

    /**
     * 模板配置列表
     */
    @ApiModelProperty(value = "模板配置列表")
    private List<NotificationTemplate> templates;
}
