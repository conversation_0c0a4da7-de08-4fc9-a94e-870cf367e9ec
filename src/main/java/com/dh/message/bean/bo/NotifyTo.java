package com.dh.message.bean.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
@ApiModel(value = "NotifyTo对象", description = "发送通知对象")
public class NotifyTo {
    /**
     * 用户ID(固定用户)
     */
    @ApiModelProperty(value = "用户ID")
    private Long uid;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String name;

    /**
     * 字段名(动态用户)
     */
    @ApiModelProperty(value = "字段名")
    private String field;

    /**
     * 扣分数值
     */
    @ApiModelProperty(value = "扣分数值")
    private BigDecimal score;
}
