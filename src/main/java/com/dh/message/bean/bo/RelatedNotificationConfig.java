package com.dh.message.bean.bo;

import com.dh.dto.enums.WeComEnum;
import com.dh.message.config.CollectionNullSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 责任人通知配置
 *
 * @program: dh-all
 * @author: zgj
 * @create: 2025-07-28 14:23
 **/
@Data
public class RelatedNotificationConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务模板
     */
    private String taskTemplate;

    /**
     * 语言（zh-CN，en-US）
     */
    private String language;

    /**
     * 企微应用名称
     */
    private String weComAppName;

    public String getWeComAppName() {
        if (StringUtils.isEmpty(weComAppName)) {
            // 如果没值，则返回默认值
            return WeComEnum.DEFAULT.name();
        }
        return weComAppName;
    }

    /**
     * 责任人通知配置
     */
    @ApiModelProperty(value = "责任人通知配置")
    @JsonSerialize(nullsUsing = CollectionNullSerialize.class)
    private List<RemindConfig> relatedNotifications;


    @Data
    public static class RemindConfig {
        /**
         * 通知类型
         */
        @ApiModelProperty(value = "通知类型")
        private String type;

        /**
         * 通知参数
         */
        @ApiModelProperty(value = "通知参数")
        private Map<String, Object> params;

        /**
         * 模板
         */
        @ApiModelProperty(value = "模板")
        private String template;
    }
}
