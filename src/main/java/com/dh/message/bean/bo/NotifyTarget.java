package com.dh.message.bean.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 通知对象配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@Data
@ApiModel(value = "NotifyTarget对象", description = "通知对象配置")
public class NotifyTarget implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID(固定用户)
     */
    @ApiModelProperty(value = "用户ID")
    private Long uid;

    /**
     * 配置名称
     */
    @ApiModelProperty(value = "配置名称")
    private String name;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 说明
     */
    @ApiModelProperty(value = "说明")
    private String explain;

    /**
     * 字段名(动态用户)
     */
    @ApiModelProperty(value = "字段名")
    private String field;

    /**
     * 扣分数值
     */
    @ApiModelProperty(value = "扣分数值")
    private BigDecimal score;

    @ApiModelProperty(value = "需要发送的用户ID列表")
    private List<Long> uids;

    /**
     * 获得实际uid
     *
     * @param map
     * @return 用户Id列表
     */
    public List<Long> getUids(Map<String, String> map) {
        if (this.field == null) {
            return this.uid == null ? Collections.emptyList() : Collections.singletonList(this.uid);
        } else {
            String uid = map.getOrDefault(this.field, "");
            if (uid.isEmpty())
                log.warn("参数错误: {}, uid is empty", this.field);

            return Arrays.stream(uid.split(",")).map(String::trim).filter(s -> !s.isEmpty()).map(Long::valueOf).collect(Collectors.toList());
        }
    }
}
