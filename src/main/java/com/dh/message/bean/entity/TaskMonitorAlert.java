package com.dh.message.bean.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 任务监控报警表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@TableName("task_monitor_alert")
@ApiModel(value="TaskMonitorAlert对象", description="任务监控报警表")
public class TaskMonitorAlert implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务监控ID
     */
    @ApiModelProperty(value = "任务监控ID")
    @TableField("tm_id")
    private Long tmId;

    /**
     * 任务监控配置ID
     */
    @ApiModelProperty(value = "任务监控配置ID")
    @TableField("tms_id")
    private Long tmsId;

    /**
     * 报警级别
     */
    @ApiModelProperty(value = "报警级别")
    @TableField("alert_level")
    private Integer alertLevel;

    /**
     * 报警信息
     */
    @ApiModelProperty(value = "报警信息")
    @TableField("message")
    private String message;

    /**
     * 报警通知人
     */
    @ApiModelProperty(value = "报警通知人")
    @TableField("to_user_id")
    private String toUserId;

    /**
     * 报警相关人
     */
    @ApiModelProperty(value = "报警相关人")
    @TableField("to_cc_users")
    private String toCcUsers;

    /**
     * 是否为预警报警
     */
    @ApiModelProperty(value = "是否为预警报警")
    @TableField("is_warning")
    private Integer isWarning;

    /**
     * 删除标识(0: 否，1:是)
     */
    @ApiModelProperty(value = "删除标识(0: 否，1:是)")
    @TableField("del_flag")
    @TableLogic
    private Integer delFlag;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_date", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateDate;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_date", fill = FieldFill.INSERT)
    private LocalDateTime createDate;


}
