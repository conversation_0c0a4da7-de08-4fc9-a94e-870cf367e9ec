package com.dh.message.bean.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 任务更新历史记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@TableName("task_monitor_history")
@ApiModel(value="TaskMonitorHistory对象", description="任务更新历史记录表")
public class TaskMonitorHistory implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务监控ID
     */
    @ApiModelProperty(value = "任务监控ID")
    @TableField("tm_id")
    private Long tmId;

    /**
     * 任务监控配置ID
     */
    @ApiModelProperty(value = "任务监控配置ID")
    @TableField("tms_id")
    private Long tmsId;

    /**
     * 更新的类型(1=>更新状态, 2=>延期任务, 3=>触发预警, 4=>触发报警)
     */
    @ApiModelProperty(value = "更新的类型(1=>更新状态, 2=>延期任务, 3=>触发预警, 4=>触发报警)")
    @TableField("type")
    private Integer type;

    /**
     * 更新值(JSON)
     */
    @ApiModelProperty(value = "更新值(JSON)")
    @TableField("update_info")
    private String updateInfo;

    /**
     * 删除标识(0: 否，1:是)
     */
    @ApiModelProperty(value = "删除标识(0: 否，1:是)")
    @TableField("del_flag")
    @TableLogic
    private Integer delFlag;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_date", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateDate;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_date", fill = FieldFill.INSERT)
    private LocalDateTime createDate;


}
