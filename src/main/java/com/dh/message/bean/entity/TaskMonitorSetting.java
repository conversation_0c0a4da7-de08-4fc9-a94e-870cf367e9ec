package com.dh.message.bean.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dh.message.bean.bo.AlarmLevelConfig;
import com.dh.message.bean.bo.NotificationConfig;
import com.dh.message.bean.bo.RelatedNotificationConfig;
import com.dh.message.config.EntityTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 任务监控配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@TableName(value = "task_monitor_setting", autoResultMap = true)
@ApiModel(value="TaskMonitorSetting对象", description="任务监控配置表")
public class TaskMonitorSetting implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务的标题
     */
    @ApiModelProperty(value = "任务的标题")
    @TableField("title")
    private String title;

    /**
     * 任务的类型(1 => 分级任务, 2 => 简单任务)
     */
    @ApiModelProperty(value = "任务的类型(1 => 分级任务, 2 => 简单任务)")
    @TableField("type")
    private Integer type;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @TableField("business_type")
    private String businessType;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    @TableField("create_by_name")
    private String createByName;

    /**
     * 通知配置
     */
    @TableField(value = "notifications", typeHandler = EntityTypeHandler.NotificationConfigListTypeHandler.class)
    private List<NotificationConfig> notifications;

    /**
     * 责任人通知配置
     */
    @ApiModelProperty(value = "责任人通知配置")
    @TableField(value = "related_notification", typeHandler = EntityTypeHandler.RelatedNotificationConfigTypeHandler.class)
    private RelatedNotificationConfig relatedNotification;

    /**
     * 报警分级设置
     */
    @TableField(value = "alarm_levels", typeHandler = EntityTypeHandler.AlarmLevelConfigListTypeHandler.class)
    private List<AlarmLevelConfig> alarmLevels;

    /**
     * 说明
     */
    @TableField(value = "`explain`")
    private String explain;

    /**
     * 删除标识(0: 否，1:是)
     */
    @ApiModelProperty(value = "删除标识(0: 否，1:是)")
    @TableField("del_flag")
    @TableLogic
    private Integer delFlag;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_date", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateDate;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_date", fill = FieldFill.INSERT)
    private LocalDateTime createDate;


}
