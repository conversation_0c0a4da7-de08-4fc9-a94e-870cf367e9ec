package com.dh.message.bean.fm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 任务监控配置表查询参数对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "TaskMonitorSettingQueryFM对象", description = "任务监控配置表查询参数对象")
public class TaskMonitorSettingQueryFM implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小")
    private Integer pageSize = 10;

    /**
     * 任务的标题
     */
    @ApiModelProperty(value = "任务的标题(模糊查询)")
    private String title;

    /**
     * 任务的类型(1 => 分级任务, 2 => 简单任务)
     */
    @ApiModelProperty(value = "任务的类型(1 => 分级任务, 2 => 简单任务)")
    private Integer type;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型(模糊查询)")
    private String businessType;


    /**
     * 业务类型数组
     */
    @ApiModelProperty(value = "业务类型数组")
    private List<String> businessTypeList;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createByName;

}
