package com.dh.message.bean.fm;

import com.dh.message.bean.bo.AlarmLevelConfig;
import com.dh.message.bean.bo.NotificationConfig;
import com.dh.message.bean.bo.RelatedNotificationConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 任务监控配置表前端传参对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "TaskMonitorSettingFM对象", description = "任务监控配置表前端传参对象")
public class TaskMonitorSettingFM implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID(更新时需要)
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 任务的标题
     */
    @ApiModelProperty(value = "任务的标题", required = true)
    @NotBlank(message = "任务标题不能为空")
    private String title;

    /**
     * 任务的类型(1 => 分级任务, 2 => 简单任务)
     */
    @ApiModelProperty(value = "任务的类型(1 => 分级任务, 2 => 简单任务)", required = true)
    @NotNull(message = "任务类型不能为空")
    private Integer type;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型", required = true)
    @NotBlank(message = "业务类型不能为空")
    private String businessType;

    /**
     * 拓展说明
     */
    @ApiModelProperty(value = "拓展说明（JSON）", required = true)
    private String explain;

    /**
     * 通知配置
     */
    @ApiModelProperty(value = "通知配置", required = true)
    @NotNull(message = "通知配置不能为空")
    private List<NotificationConfig> notifications;

    /**
     * 责任人通知配置
     */
    @ApiModelProperty(value = "责任人通知配置")
    @NotNull(message = "责任人通知配置")
    private RelatedNotificationConfig relatedNotification;

    /**
     * 报警分级设置
     */
    @ApiModelProperty(value = "报警分级设置", required = true)
    @NotNull(message = "报警分级设置不能为空")
    private List<AlarmLevelConfig> alarmLevels;
}
