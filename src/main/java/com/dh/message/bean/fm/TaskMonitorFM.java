package com.dh.message.bean.fm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
public class TaskMonitorFM {

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 查询开始时间开始
     */
    @ApiModelProperty(value = "查询开始时间开始")
    private LocalDate searchDateBegin;

    /**
     * 查询开始时间结束
     */
    @ApiModelProperty(value = "查询开始时间结束")
    private LocalDate searchDateEnd;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小")
    private Integer pageSize = 10;
}
