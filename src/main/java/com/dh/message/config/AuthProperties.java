//package com.dh.message.config;
//
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Configuration;
//
//import javax.validation.constraints.NotEmpty;
//import java.util.List;
//
//@Configuration
//@ConfigurationProperties(prefix = "auth")
//@Data
//public class AuthProperties {
//    /**
//     * 不验证的路径，路径匹配规则是依照Ant
//     * 基本规则:
//     * <ul>
//     * <li>"?" 匹配一个字符（除过操作系统默认的文件分隔符）</li>
//     * <li>"*" 匹配0个或多个字符</li>
//     * <li>"**" 匹配0个或多个目录</li>
//     * <li>"{spring:[a-z]+}" 将正则表达式[a-z]+匹配到的值,赋值给名为 spring 的路径变量.(必须是完全匹配才行,在SpringMVC中只有完全匹配才会进入controller层的方法)</li>
//     * </ul>
//     */
//    private List<String> excludedPaths;
//
//    /**
//     * 服务端验证认证信息地址
//     **/
//    @NotEmpty
//    private String loginUrl;
//}
