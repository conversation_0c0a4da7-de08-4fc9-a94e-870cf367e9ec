package com.dh.message.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "aliyun-sms-config")
public class AliyunSmsConfig {
    /**
     * 超时时间
     */
    private Integer defaultConnectTimeout;
    /**
     * 超时时间
     */
    private Integer defaultReadTimeout;
    /**
     * key
     */
    private String accessKeyId;
    /**
     * secret
     */
    private String accessKeySecret;
//    /**
//     * 短信签名-可在短信控制台中找到
//     */
//    private String signName;
//    /**
//     * 短信模板-可在短信控制台中找到
//     */
//    private String templateCode;

    /*public String getSignName() {
        return "鼎衡智能管理通知";
    }*/
}
