//package com.dh.message.config;
//
//import com.dh.message.filter.AuthUserFilter;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.springframework.boot.web.client.RestTemplateBuilder;
//import org.springframework.boot.web.servlet.FilterRegistrationBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.http.MediaType;
//import org.springframework.http.converter.HttpMessageConverter;
//import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
//import org.springframework.web.client.RestTemplate;
//import org.springframework.web.cors.CorsConfiguration;
//import org.springframework.web.cors.CorsConfigurationSource;
//import org.springframework.web.filter.CorsFilter;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Configuration
//public class WebConfig implements WebMvcConfigurer {
//
//    @Bean
//    public RestTemplate restTemplate(RestTemplateBuilder builder) {
//        List<HttpMessageConverter<?>> converters = builder.build().getMessageConverters();
//        for (HttpMessageConverter<?> converter : converters) {
//            //因为我们只想要jsonConverter支持对text/html的解析
//            if (converter instanceof MappingJackson2HttpMessageConverter) {
//                try {
//                    //先将原先支持的MediaType列表拷出
//                    List<MediaType> mediaTypeList = new ArrayList<>(converter.getSupportedMediaTypes());
//                    //加入对text/html的支持
//                    mediaTypeList.add(MediaType.TEXT_HTML);
//                    //将已经加入了text/html的MediaType支持列表设置为其支持的媒体类型列表
//                    ((MappingJackson2HttpMessageConverter) converter).setSupportedMediaTypes(mediaTypeList);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//        return builder.build();
//    }
//
//    @Bean
//    public CorsConfigurationSource corsConfigurationSource() {
//        return request -> {
//            CorsConfiguration corsConfiguration = new CorsConfiguration();
//            corsConfiguration.addAllowedOrigin("*");
//            corsConfiguration.addAllowedHeader("*");
//            corsConfiguration.setAllowCredentials(true);
//            corsConfiguration.addAllowedMethod("GET");
//            corsConfiguration.addAllowedMethod("POST");
//            corsConfiguration.addAllowedMethod("PUT");
//            corsConfiguration.addAllowedMethod("DELETE");
//            corsConfiguration.addAllowedMethod("OPTIONS");
//            corsConfiguration.setMaxAge(3600L);
//            return corsConfiguration;
//        };
//    }
//
//    @Bean
//    public FilterRegistrationBean<CorsFilter> corsFilterRegistrationBean(CorsConfigurationSource corsConfigurationSource) {
//        CorsFilter filter = new CorsFilter(corsConfigurationSource);
//        FilterRegistrationBean<CorsFilter> registrationBean = new FilterRegistrationBean<>(filter);
//        registrationBean.setOrder(0);
//        registrationBean.addUrlPatterns("/*");
//        return registrationBean;
//    }
//
//    @Bean
//    public FilterRegistrationBean<AuthUserFilter> authUserFilterRegistrationBean(
//            AuthProperties authProperties, RestTemplate restTemplate, ObjectMapper objectMapper) {
//        AuthUserFilter filter = new AuthUserFilter(authProperties, restTemplate, objectMapper);
//        FilterRegistrationBean<AuthUserFilter> registrationBean = new FilterRegistrationBean<>(filter);
//        // 设置AuthUserFilter的顺序为1, 在CorsFilter之后，防止验证导致的跨域失效
//        registrationBean.setOrder(1);
//        registrationBean.addUrlPatterns("/*");
//        return registrationBean;
//    }
//}
