package com.dh.message.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * 集合为空判断
 *
 * @program: dh
 * @author: zgj
 * @create: 2023-08-14 16:02
 **/
public class CollectionNullSerialize extends JsonSerializer<Object> {

    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeStartArray();
            gen.writeEndArray();
        }else {
            serializers.defaultSerializeValue(value, gen);
        }
    }
}
