package com.dh.message.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "system-config")
public class SystemConfig {

    /**
     * 最大推送次数
     */
    private Integer maxSendNum;

    /**
     * 失败消息推送间隔时间
     */
    private Integer failSendTime;
}
