package com.dh.message.config;

import com.dh.message.bean.bo.AlarmLevelConfig;
import com.dh.message.bean.bo.NotificationConfig;
import com.dh.message.bean.bo.RelatedNotificationConfig;
import com.fasterxml.jackson.core.type.TypeReference;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 实体类映射
 *
 * @program: dh-all
 * @author: zgj
 * @create: 2025-07-24 21:01
 **/
public class EntityTypeHandler {

    /**
     * Map<String, LocalDateTime>
     */
    public static class LocalDateTimeMapTypeHandler extends JsonTypeHandler {
        public LocalDateTimeMapTypeHandler() {
            super.type = new TypeReference<Map<String, LocalDateTime>>() {
            };
        }
    }

    public static class AlarmLevelConfigListTypeHandler extends JsonTypeHandler {
        public AlarmLevelConfigListTypeHandler() {
            super.type = new TypeReference<List<AlarmLevelConfig>>() {
            };
        }
    }

    public static class NotificationConfigListTypeHandler extends JsonTypeHandler {
        public NotificationConfigListTypeHandler() {
            super.type = new TypeReference<List<NotificationConfig>>() {
            };
        }
    }

    public static class RelatedNotificationConfigTypeHandler extends JsonTypeHandler {
        public RelatedNotificationConfigTypeHandler() {
            super.type = new TypeReference<RelatedNotificationConfig>() {
            };
        }
    }
}
