//package com.dh.message.config;
//
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.stereotype.Component;
//
///**
// * @project: easypoi_1
// * @ClassName: MyProps
// * @author: Bin
// * @creat: 2021/4/6 13:16
// * 描述:调用第三方接口
// */
//@Component
//@Data  //简写get  set 的注解
//@ConfigurationProperties(prefix="system") //接收application.yml中的myProps下面的属性
//public class EtempConfig {
//    private String Robot;//群聊机器人接口
//}
