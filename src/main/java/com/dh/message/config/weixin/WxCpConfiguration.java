//package com.dh.message.config.weixin;
//
//import me.chanjar.weixin.cp.api.WxCpService;
//import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
//import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * 微信企业号配置
// */
//@Configuration
//@EnableConfigurationProperties(WxCpProperties.class)
//public class WxCpConfiguration {
//
//    private final WxCpProperties wxCpProperties;
//
//    public WxCpConfiguration(WxCpProperties wxCpProperties) {
//        this.wxCpProperties = wxCpProperties;
//    }
//
//    @Bean
//    public WxCpService wxCpService() {
//
//        WxCpDefaultConfigImpl config = new WxCpDefaultConfigImpl();
//        config.setCorpId(wxCpProperties.getCorPid());
//        config.setCorpSecret(wxCpProperties.getCorpSecret());
//        config.setAgentId(wxCpProperties.getAgentId());
//        // 设置微信企业号应用的token
//        // config.setToken("...");
//        // 设置微信企业号应用的EncodingAESKey
//        // config.setAesKey("...");
//
//        WxCpService wxCpService = new WxCpServiceImpl();
//        wxCpService.setWxCpConfigStorage(config);
//
//        return wxCpService;
//    }
//}
