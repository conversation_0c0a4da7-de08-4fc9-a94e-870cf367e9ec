package com.dh.message.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.dh.common.constant.DbConstant;
import com.dh.framework.security.util.SecurityUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * Mybatis-plus 配置
 */
@EnableTransactionManagement
@Configuration
@MapperScan(basePackageClasses = {com.dh.message.mapper.BasePackageInfo.class})
public class MybatisPlusConfig {

    /*
     * 分页插件，自动识别数据库类型
     * 多租户，请参考官网【插件扩展】
     */
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        return new PaginationInterceptor();
    }

    /**
     * 打印 sql
     */
    /*@Bean
    public PerformanceInterceptor performanceInterceptor() {
        PerformanceInterceptor performanceInterceptor = new PerformanceInterceptor();
        //格式化sql语句
        Properties properties = new Properties();
        properties.setProperty("format", "true");
        performanceInterceptor.setProperties(properties);
        return performanceInterceptor;
    }*/

    /**
     * mybaits-plus 字段自动填充
     *
     * @return MetaObjectHandler
     */
    @Bean
    public MetaObjectHandler myMetaObjectHandler() {
        return new MetaObjectHandler() {
            @Override
            public void insertFill(MetaObject metaObject) {
                Long createBy = Optional.ofNullable(SecurityUtil.getUserId()).orElse(0L);
                setInsertFieldValByName(DbConstant.FIELD_CREATE_BY.getValue(), createBy, metaObject);
                setInsertFieldValByName(DbConstant.FIELD_CREATE_DATE.getValue(), LocalDateTime.now(), metaObject);
                setInsertFieldValByName(DbConstant.FIELD_UPDATE_BY.getValue(), createBy, metaObject);
                setInsertFieldValByName(DbConstant.FIELD_UPDATE_DATE.getValue(), LocalDateTime.now(), metaObject);
            }

            @Override
            public void updateFill(MetaObject metaObject) {
                Long updateBy = Optional.ofNullable(SecurityUtil.getUserId()).orElse(0L);
                setUpdateFieldValByName(DbConstant.FIELD_UPDATE_BY.getValue(),updateBy, metaObject);
                setUpdateFieldValByName(DbConstant.FIELD_UPDATE_DATE.getValue(),LocalDateTime.now(), metaObject);
            }
        };
    }
}
