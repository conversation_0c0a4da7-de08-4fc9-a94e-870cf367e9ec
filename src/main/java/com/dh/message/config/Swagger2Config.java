package com.dh.message.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.ApiKey;
import springfox.documentation.service.AuthorizationScope;
import springfox.documentation.service.SecurityReference;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/4/28
 */
@Configuration
@EnableSwagger2
public class Swagger2Config {

    //访问地址：http://localhost:port/your-app-root/swagger-ui.html
    //eg：http://localhost:8765/admin/swagger-ui.html
    @Bean
    public Docket docket1() {
        return new Docket(DocumentationType.SWAGGER_2).groupName("yqw");
    }

    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("lzx")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.dh.message"))
                .paths(PathSelectors.any())//过滤
                .build()
                .securitySchemes(securitySchemes())
                .securityContexts(securityContexts());
        //配置全局亲求参数
        //.globalOperationParameters(pars);
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("dh-消息中心接口文档")
                .description("dh-消息中心接口文档")
                .version("1.0")
                .build();
    }

    // 通过Swagger2的securitySchemes配置全局参数：如下列代码所示，securitySchemes的ApiKey中增加一个名为“token”，
    // type为“header”的参数。
    private List<ApiKey> securitySchemes() {
        List<ApiKey> list = new ArrayList<>();
        list.add(new ApiKey("token", "token", "header"));
        list.add(new ApiKey("Accept-Language", "Accept-Language", "header"));
        return list;
    }

    private List<SecurityContext> securityContexts() {
        List<SecurityContext> contexts = new ArrayList<>();
        contexts.add(SecurityContext.builder()
                .securityReferences(defaultAuth())
                .forPaths(PathSelectors.regex("^(?!auth).*$"))
                .build());
        return contexts;
    }

    List<SecurityReference> defaultAuth() {
        AuthorizationScope[] authorizationScopes = new AuthorizationScope[1];
        authorizationScopes[0] = new AuthorizationScope("global", "accessEverything");
        List<SecurityReference> references = new ArrayList<>();
        references.add(new SecurityReference("token", authorizationScopes));
        references.add(new SecurityReference("Accept-Language", authorizationScopes));
        return references;
    }

}
