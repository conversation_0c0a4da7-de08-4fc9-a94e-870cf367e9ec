package com.dh.message.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <p>
 * 消息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "MessageInfo对象", description = "消息表")
public class MessageInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ID_WORKER)
    private Long id;

    @ApiModelProperty(value = "消息类型（1:我的待办，2:我的通知，3:公司公告）")
    @TableField("msg_type")
    private Integer msgType;

    @ApiModelProperty(value = "消息主题")
    @TableField("subject")
    private String subject;

    @ApiModelProperty(value = "消息内容")
    @TableField("content")
    private String content;

    @ApiModelProperty(value = "消息跳转链接")
    @TableField("msg_url")
    private String msgUrl;

    @ApiModelProperty(value = "业务主键id,用于更新待办状态")
    @TableField("business_id")
    private String businessId;

    @ApiModelProperty(value = "业务类型(举例子1:初审，2：复审)")
    @TableField("business_type")
    private String businessType;

    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "创建人姓名")
    private String createByName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_date", fill = FieldFill.INSERT)
    private LocalDateTime createDate;

    @ApiModelProperty(value = "接收人")
    @TableField("receive_to")
    private Long receiveTo;

    @ApiModelProperty(value = "是否已读（0：否，1：是）")
    @TableField("read_flag")
    private Integer readFlag;

    @ApiModelProperty(value = "是否处理（0：否，1：是）")
    @TableField("handle_flag")
    private Integer handleFlag;

    @ApiModelProperty(value = "处理时间")
    @TableField("handle_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime handleDate;

    @ApiModelProperty(value = "处理截止时间（字符串）")
    @TableField("deadline_date")
    private String deadlineDate;

    @ApiModelProperty(value = "参数集合")
    @TableField("params_value")
    private String paramsValue;

    @ApiModelProperty(value = "消息模板id")
    @TableField("message_template_id")
    private Long messageTemplateId;

    @ApiModelProperty(value = "消息模板参数")
    @TableField("message_template_param")
    private String messageTemplateParam;

    @ApiModelProperty(value = "是否弹窗(0否1是)")
    @TableField("window_flag")
    private Integer windowFlag;

    @ApiOperation("处理时限显示")
    public String getDeadlineStr() {
        if (StringUtils.isEmpty(this.getDeadlineDate()) || this.getMsgType() != 1) {
            return null;
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String dealineDateStr = this.getDeadlineDate() == null ? null : this.getDeadlineDate() + " 00:00:00";
        LocalDateTime dealineDate = LocalDateTime.parse(dealineDateStr, df);
        if (createDate.isBefore(dealineDate)) {
            Duration duration = Duration.between(createDate, dealineDate);
            Long days = duration.toDays() + 1;
            return "剩" + days + "天";
        } else {
            Duration duration = Duration.between(dealineDate, createDate);
            Long days = duration.toDays() + 1;
            return "超" + days + "天";
        }
    }

}
