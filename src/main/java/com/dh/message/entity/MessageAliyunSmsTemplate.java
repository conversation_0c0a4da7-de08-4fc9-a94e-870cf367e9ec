package com.dh.message.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 阿里云短信模版信息
 * </p>
 *
 * <AUTHOR> @since 2020-05-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "MessageAliyunSmsTemplate对象", description = "阿里云短信模版信息")
public class MessageAliyunSmsTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "template_code", type = IdType.ID_WORKER)
    private String templateCode;

    @TableField("template_name")
    private String templateName;

    @TableField("template_content")
    private String templateContent;

    @TableField("template_type")
    private Integer templateType;

    @TableField("params_json_str")
    private String paramsJsonStr;

    @ApiModelProperty(value = "参数个数")
    @TableField("params_count")
    private Integer paramsCount;

    @TableField("del_flag")
    @TableLogic
    private Integer delFlag;

    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;

    @TableField(value = "create_date", fill = FieldFill.INSERT)
    private LocalDateTime createDate;

    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @TableField(value = "update_date", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateDate;

}
