package com.dh.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 非点击消除代办URL配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Data
@TableName("dh_message_url")
@ApiModel(value="DhMessageUrl对象", description="非点击消除代办URL配置表")
public class DhMessageUrl implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消息跳转链接
     */
    @ApiModelProperty(value = "消息跳转链接")
    @TableField("msg_url")
    private String msgUrl;


}
