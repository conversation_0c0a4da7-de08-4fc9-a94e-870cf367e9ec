package com.dh.message.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MessagePushTaskRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer type; //执行类型 1 首次推送 2 失败推送 3 失败数据处理
    private Integer recordNum; //总记录数
    private Integer successNum; //成功记录数
    private Integer failNum; //失败记录数

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime; // 创建时间

}
