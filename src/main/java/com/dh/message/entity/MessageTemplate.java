package com.dh.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 消息模板
 * </p>
 *
 * <AUTHOR> @since 2021-01-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "MessageTemplate对象", description = "消息模板")
public class MessageTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ID_WORKER)
    private Long id;

    @ApiModelProperty(value = "模板分组Id")
    @TableField("template_group_id")
    private String templateGroupId;

    @ApiModelProperty(value = "模板编码")
    @TableField("template_code")
    private String templateCode;

    @ApiModelProperty(value = "模板内容")
    @TableField("template_content")
    private String templateContent;

    @ApiModelProperty(value = "模板名称")
    @TableField("template_name")
    private String templateName;

    @ApiModelProperty(value = "描述")
    @TableField("describes")
    private String describes;

    @ApiModelProperty(value = "消息类型（1:待办，2:通知、消息）")
    @TableField("msg_type")
    private Integer msgType;

    @ApiModelProperty(value = "参数json")
    @TableField("params_json")
    private String paramsJson;

    @ApiModelProperty(value = "参数个数")
    @TableField("params_num")
    private Integer paramsNum;
}
