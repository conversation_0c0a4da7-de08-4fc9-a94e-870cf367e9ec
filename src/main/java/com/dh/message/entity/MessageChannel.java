package com.dh.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 消息渠道表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "MessageChannel对象", description = "消息渠道表")
public class MessageChannel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ID_WORKER)
    private Long id;

    @ApiModelProperty(value = "消息主键id")
    @TableField("message_info_id")
    private Long messageInfoId;

    @ApiModelProperty(value = "消息渠道（1 : PC 2 : APP 3 : 短信 4 : 邮件）")
    @TableField("message_channel")
    private Integer messageChannel;

    @ApiModelProperty(value = "渠道业务号码（例如：手机号，邮箱，微信openId）")
    @TableField("channel_number")
    private String channelNumber;

    @ApiModelProperty(value = "发送模板参数(多个参数用 , 隔开)")
    @TableField("template_param")
    private String templateParam;

    @ApiModelProperty(value = "发送模板编码")
    @TableField("template_code")
    private String templateCode;

}
