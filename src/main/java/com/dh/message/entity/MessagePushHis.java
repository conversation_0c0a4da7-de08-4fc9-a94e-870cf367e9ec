package com.dh.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "MessagePushHis", description = "消息推送归档表")
public class MessagePushHis implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;
    @ApiModelProperty(value = "消息类型")
    private Integer msgType;
    @ApiModelProperty(value = "推送渠道")
    private Integer sendChannel;
    @ApiModelProperty(value = "消息主题")
    private String subject;
    @ApiModelProperty(value = "消息内容")
    private String content;
    @ApiModelProperty(value = "消息跳转链接")
    private String msgUrl;
    @ApiModelProperty(value = "消息接收人")
    private String receiveTo;
    @ApiModelProperty(value = "消息接受人业务号码 例如：手机号码，微信openid等")
    private String receiveToNum;
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @ApiModelProperty(value = "0 无效  1有效")
    private Integer status;
    @ApiModelProperty(value = "0 未推送 1推送成功 2 推送失败")
    private Integer sendStatus;

    @ApiModelProperty(value = "推送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendTime;
    @ApiModelProperty(value = "推送失败次数")
    private Integer sendFailTimes;
    @ApiModelProperty(value = "推送失败原因")
    private String sendFailMsg;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime msgArchiveTime; //归档时间

}
