package com.dh.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 阿里云短信发送记录
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "MessageAliyunSmsRecord对象", description = "阿里云短信发送记录")
public class MessageAliyunSmsRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ID_WORKER_STR)
    private String id;

    @ApiModelProperty(value = "模板")
    @TableField("template_code")
    private String templateCode;

    @ApiModelProperty(value = "电话号码 多个 逗号分割")
    @TableField("phone_numbers")
    private String phoneNumbers;

    @ApiModelProperty(value = "返回code")
    @TableField("return_code")
    private String returnCode;

    @ApiModelProperty(value = "返回msg")
    @TableField("return_msg")
    private String returnMsg;

    @ApiModelProperty(value = "返回requestid")
    @TableField("return_requestid")
    private String returnRequestid;

    @ApiModelProperty(value = "返回bizid")
    @TableField("return_bizid")
    private String returnBizid;

    @ApiModelProperty(value = "请求参数")
    @TableField("params")
    private String params;

    @ApiModelProperty(value = "返回全文")
    @TableField("return_str")
    private String returnStr;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}
