package com.dh.message.service.impl.strategy;

import com.dh.common.util.R;
import com.dh.dto.bean.dto.messagerelay.ToPushUserIdDTO;
import com.dh.dto.enums.WeComEnum;
import com.dh.dto.feign.WeComFeignService;
import com.dh.message.bean.bo.NotificationConfig;
import com.dh.message.bean.bo.NotifyTarget;
import com.dh.message.bean.bo.NotifyTo;
import com.dh.message.bean.bo.RelatedNotificationConfig;
import com.dh.message.dto.TaskMonitorInfoDTO;
import com.dh.message.enums.NotificationTypeEnum;
import com.dh.message.service.NotificationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.Future;

/**
 * 微信通知策略实现
 *
 * <AUTHOR>
 * @version 0.9.1
 * @since 2025/7/25
 */
@Service
@Slf4j
public class WechatNotificationStrategy implements NotificationStrategy {
    @Resource
    private WeComFeignService weComFeignService;

    @Override
    public String getType() {
        return NotificationTypeEnum.TYPE_WECHAT.getName();
    }

    @Override
    @Async("sendPoolExecutor")
    public Future<Boolean> send(String template, NotifyTo notifyTo, TaskMonitorInfoDTO taskMonitorInfoDTO,
                                NotificationConfig notificationConfig) {
        try {
            //log.info("发送微信通知: 模板={}", template, notifyTarget);
            R<Long> res = weComFeignService.toPushUserId(new ToPushUserIdDTO(notifyTo.getUid(), template,
                    WeComEnum.TASK_ALARM));
            log.info("发送微信通知结果: {} - {}", notifyTo, res);

            return AsyncResult.forValue(true);
        } catch (Exception e) {
            log.error("发送微信通知失败: 模板={}, 目标={}", template, notifyTo, e);
            return AsyncResult.forValue(false);
        }
    }

    @Override
    public Future<Boolean> send(String template, Long userId, TaskMonitorInfoDTO taskMonitorInfoDTO, RelatedNotificationConfig.RemindConfig relatedNotificationConfig) {
        try {
            //log.info("发送微信通知: 模板={}", template, notifyTarget);
            R<Long> res = weComFeignService.toPushUserId(new ToPushUserIdDTO(userId, template,
                    WeComEnum.valueOf(taskMonitorInfoDTO.getRelatedNotification().getWeComAppName()))
            );
            log.info("发送微信通知结果: {} - {}", userId, res);
            return AsyncResult.forValue(true);
        } catch (Exception e) {
            log.error("发送微信通知失败: 模板={}, 目标={}", template, userId, e);
            return AsyncResult.forValue(false);
        }
    }


    @Override
    public boolean validate(NotifyTarget notifyTarget) {
        if (notifyTarget == null) {
            log.warn("微信通知目标为空");
            return false;
        }


        if (notifyTarget.getUid() == null && notifyTarget.getField() == null) {
            log.warn("微信通知目标为空");
            return false;
        }

        return true;
    }
} 