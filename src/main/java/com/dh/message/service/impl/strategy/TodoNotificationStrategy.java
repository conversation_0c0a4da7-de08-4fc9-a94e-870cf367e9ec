package com.dh.message.service.impl.strategy;

import com.dh.message.bean.bo.NotificationConfig;
import com.dh.message.bean.bo.NotifyTarget;
import com.dh.message.bean.bo.NotifyTo;
import com.dh.message.bean.bo.RelatedNotificationConfig;
import com.dh.message.constant.MessageConstant;
import com.dh.message.dto.TaskMonitorInfoDTO;
import com.dh.message.entity.MessageInfo;
import com.dh.message.enums.NotificationTypeEnum;
import com.dh.message.service.IMessageInfoService;
import com.dh.message.service.NotificationStrategy;
import com.dh.message.service.impl.NotificationImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Optional;
import java.util.concurrent.Future;

/**
 * 代办策略实现
 *
 * <AUTHOR>
 * @version 0.9.1
 * @since 2025/7/25
 */
@Service
@Slf4j
public class TodoNotificationStrategy implements NotificationStrategy {
    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private IMessageInfoService messageInfoService;

    @Override
    public String getType() {
        return NotificationTypeEnum.TYPE_TODO.getName();
    }

    @Override
    @Async("sendPoolExecutor")
    public Future<Boolean> send(String template, NotifyTo notifyTo,
                                TaskMonitorInfoDTO taskMonitorInfoDTO, NotificationConfig notificationConfig) {
        try {
            log.info("发送代办: 模板={}, 目标={}", template, notifyTo);

            if (!notifyTo.getField().equals(NotificationImpl.RELATED_UID_FIELD)) {
                log.debug("代办只能发送直接负责人");
                return AsyncResult.forValue(false);
            }

            MessageInfo messageInfo = new MessageInfo();
            messageInfo.setMsgType(MessageConstant.TASKS);
            messageInfo.setBusinessId(taskMonitorInfoDTO.getBusinessId().toString());
            messageInfo.setSubject(template);
            messageInfo.setReceiveTo(notifyTo.getUid());
            String redirectUrl = (Optional.ofNullable(taskMonitorInfoDTO.getExtParams()).orElse(Collections.emptyMap())).getOrDefault(
                    "redirect_url", "").toString();
            if (redirectUrl.isEmpty()) {
                redirectUrl = (Optional.ofNullable(notificationConfig.getParams()).orElse(Collections.emptyMap())).getOrDefault(
                        "redirect_url", "").toString();
            }
            messageInfo.setMsgUrl(redirectUrl);
            messageInfo.setCreateBy(0L);
            messageInfo.setCreateByName("任务监控系统");
            messageInfo.setCreateDate(LocalDateTime.now());
            messageInfo.setParamsValue(objectMapper.writeValueAsString(taskMonitorInfoDTO.getExtParams()));
            if (taskMonitorInfoDTO.getExtParams() != null) {
                messageInfo.setBusinessType(notificationConfig.getParams().getOrDefault("business_type", "").toString());
            }

            messageInfoService.save(messageInfo);

            return AsyncResult.forValue(true);
        } catch (Exception e) {
            log.error("发送代办失败: 模板={}, 目标={}", template, notifyTo, e);
            return AsyncResult.forValue(false);
        }
    }

    @Override
    public Future<Boolean> send(String template, Long userId, TaskMonitorInfoDTO taskMonitorInfoDTO, RelatedNotificationConfig.RemindConfig relatedNotificationConfig) {
        try {
            log.info("发送代办: 模板={}, 目标={}", template, userId);

            MessageInfo messageInfo = new MessageInfo();
            messageInfo.setMsgType(MessageConstant.TASKS);
            messageInfo.setBusinessId(taskMonitorInfoDTO.getBusinessId().toString());
            messageInfo.setSubject(template);
            messageInfo.setReceiveTo(userId);
            String redirectUrl = (Optional.ofNullable(taskMonitorInfoDTO.getExtParams()).orElse(Collections.emptyMap())).getOrDefault(
                    "redirect_url", "").toString();
            if (redirectUrl.isEmpty()) {
                redirectUrl = (Optional.ofNullable(relatedNotificationConfig.getParams()).orElse(Collections.emptyMap())).getOrDefault(
                        "redirect_url", "").toString();
            }
            messageInfo.setMsgUrl(redirectUrl);
            messageInfo.setCreateBy(0L);
            messageInfo.setCreateByName("任务监控系统");
            messageInfo.setCreateDate(LocalDateTime.now());
            messageInfo.setParamsValue(objectMapper.writeValueAsString(taskMonitorInfoDTO.getExtParams()));
            if (taskMonitorInfoDTO.getExtParams() != null) {
                messageInfo.setBusinessType(relatedNotificationConfig.getParams().getOrDefault("business_type", "").toString());
            }

            messageInfoService.save(messageInfo);

            return AsyncResult.forValue(true);
        } catch (Exception e) {
            log.error("发送代办失败: 模板={}, 目标={}", template, userId, e);
            return AsyncResult.forValue(false);
        }
    }

    @Override
    public boolean validate(NotifyTarget notifyTarget) {
        if (notifyTarget == null) {
            log.warn("通知目标为空");
            return false;
        }

        return true;
    }
} 