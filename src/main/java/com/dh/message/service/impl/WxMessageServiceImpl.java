//package com.dh.message.service.impl;
//
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.dh.message.config.EtempConfig;
//import com.dh.message.entity.Contx;
//import com.dh.message.service.WxMessageService;
//import com.dh.message.util.HttpUtils;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * 微信企业号消息服务
// */
//@Slf4j
//@AllArgsConstructor
//@Service
//public class WxMessageServiceImpl implements WxMessageService {
//
//    private final EtempConfig etempConfig;
//
//    @Override
//    public JSONObject Robot_To_Message(Contx contx) {
//        Map<String,Object> paramsMap1=new HashMap<String,Object>();
//        paramsMap1.put("msgtype","text");
//        paramsMap1.put("text",contx);
//        String tokenStr="";
//        tokenStr= HttpUtils.httpPostMethod(etempConfig.getRobot(),paramsMap1);
//        //用fastJson 解析一波
//        JSONObject object= JSON.parseObject(tokenStr);
//        return object;
//    }
//}
