package com.dh.message.service.impl;

import com.dh.message.entity.MessagePushHis;
import com.dh.message.mapper.MessagePushHisMapper;
import com.dh.message.service.MessagePushHisService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("messagePushHisServiceImpl")
@AllArgsConstructor
public class MessagePushHisServiceImpl implements MessagePushHisService {

    private MessagePushHisMapper messagePushHisMapper;

    public MessagePushHis getMessagePushHis(String messagePushId) {
        return messagePushHisMapper.selectById(messagePushId);
    }

    /**
     * 保存推送消息归档（多个）
     *
     * @param messagePushHisList
     * @return
     */
    @Override
    public void saveMessagePushHisList(List<MessagePushHis> messagePushHisList) {
        for (MessagePushHis messagePushHis : messagePushHisList) {
            messagePushHisMapper.insert(messagePushHis);
        }
    }

    /**
     * 保存推送消息归档（单个）
     *
     * @param messagePushHis
     * @return
     */
    @Override
    public boolean saveMessagePushHis(MessagePushHis messagePushHis) {
        int result = messagePushHisMapper.insert(messagePushHis);
        return result > 0;
    }

}
