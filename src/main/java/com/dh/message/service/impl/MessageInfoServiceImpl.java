package com.dh.message.service.impl;

import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dh.framework.security.util.SecurityUtil;
import com.dh.message.config.AliyunSmsConfig;
import com.dh.message.constant.MessageConstant;
import com.dh.message.controller.vo.DeleteMessageCmd;
import com.dh.message.controller.vo.HandleMessageCmd;
import com.dh.message.dto.MessageChannelDTO;
import com.dh.message.dto.MessageInfoDTO;
import com.dh.message.entity.MessageChannel;
import com.dh.message.entity.MessageInfo;
import com.dh.message.entity.MessagePush;
import com.dh.message.mapper.MessageInfoMapper;
import com.dh.message.service.IMessageAliyunSmsRecordService;
import com.dh.message.service.IMessageChannelService;
import com.dh.message.service.IMessageInfoService;
import com.dh.message.service.MessagePushService;
import com.dh.message.util.AliyunSmsUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 消息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-09
 */
@Slf4j
@Service
@AllArgsConstructor
public class MessageInfoServiceImpl extends ServiceImpl<MessageInfoMapper, MessageInfo> implements IMessageInfoService {

    private IMessageChannelService messageChannelService;

    private MessagePushService messagePushService;

    private AliyunSmsConfig aliyunSmsConfig;

    private IMessageAliyunSmsRecordService messageAliyunSmsRecordService;

    @Override
    public void saveMessageInfo(MessageInfoDTO messageInfoDTO) {
        MessageInfo messageInfo = new MessageInfo();
        BeanUtils.copyProperties(messageInfoDTO, messageInfo);
        messageInfo.setCreateDate(messageInfoDTO.getCreateDate());
        if (this.save(messageInfo)) {
            List<MessageChannelDTO> channelDTOList = messageInfoDTO.getMessageChannelList();
            if (!CollectionUtils.isEmpty(channelDTOList)) {
                List<MessageChannel> channelList = new ArrayList<>();
                for (MessageChannelDTO channelDTO : channelDTOList) {
                    MessageChannel channel = new MessageChannel();
                    channel.setMessageInfoId(messageInfo.getId());
                    channel.setChannelNumber(channelDTO.getChannelNumber());
                    channel.setMessageChannel(channelDTO.getMessageChannel());
                    channel.setTemplateParam(channelDTO.getTemplateParam());
                    channel.setTemplateCode(channelDTO.getTemplateCode());
                    channelList.add(channel);
                }
                messageChannelService.saveBatch(channelList);
                //推送消息
                messagePush(messageInfo, channelList);
            }

        }
    }

    @Override
    public IPage<MessageInfo> findMessageByCondition(Page page, Integer messageType, String subject) {
        Long userId = SecurityUtil.getUserId();
        return baseMapper.findMessageByCondition(page, messageType, subject, userId);
    }

    @Override
    public List<MessageInfo> getByBusinessId(String businessId, String receiveTo) {
        LambdaQueryWrapper<MessageInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageInfo::getBusinessId, businessId);
        queryWrapper.eq(MessageInfo::getMsgType, MessageConstant.TASKS);
        queryWrapper.eq(receiveTo != null, MessageInfo::getReceiveTo, receiveTo);
        queryWrapper.eq(MessageInfo::getHandleFlag, 0);
        return this.list(queryWrapper);
    }

    @Override
    public List<MessageInfo> getByBusinessIdAndType(String businessId, String businessType, Long receiveTo) {
        LambdaQueryWrapper<MessageInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageInfo::getBusinessId, businessId);
        queryWrapper.eq(StringUtils.hasText(businessType), MessageInfo::getBusinessType, businessType);
        queryWrapper.eq(receiveTo != null, MessageInfo::getReceiveTo, receiveTo);
        queryWrapper.eq(MessageInfo::getMsgType, MessageConstant.TASKS);
        queryWrapper.eq(MessageInfo::getHandleFlag, 0);
        return this.list(queryWrapper);
    }

    @Override
    public MessageInfo findMessageExist(String businessId, String businessType, int msgType, Long receiveTo) {
        LambdaQueryWrapper<MessageInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageInfo::getBusinessId, businessId);
        queryWrapper.eq(MessageInfo::getBusinessType, businessType);
        queryWrapper.eq(MessageInfo::getMsgType, msgType);
        queryWrapper.eq(MessageInfo::getReceiveTo, receiveTo);
        return this.getOne(queryWrapper);
    }

    private void messagePush(MessageInfo messageInfo, List<MessageChannel> channelList) {
        for (MessageChannel channel : channelList) {
            if (channel.getMessageChannel() != 1) {
                MessagePush push = new MessagePush();
                push.setMsgType(messageInfo.getMsgType());
                push.setSendChannel(channel.getMessageChannel());
                push.setSubject(messageInfo.getSubject());
                push.setContent(messageInfo.getContent());
                push.setMsgUrl(messageInfo.getMsgUrl());
                push.setReceiveTo(messageInfo.getReceiveTo() == null ? "" : messageInfo.getReceiveTo().toString());
                push.setReceiveToNum(channel.getChannelNumber());
                push.setCreateBy(messageInfo.getCreateBy().toString());
                push.setCreateTime(LocalDateTime.now());
                messagePushService.insertMessagePush(push);
            }
            if (channel.getMessageChannel() == 3 && !"".equals(channel.getTemplateCode())) { //调临时发送短信接口
                try {
                    SendSmsResponse sendSmsResponse = AliyunSmsUtils.sendSms(aliyunSmsConfig,
                            messageAliyunSmsRecordService,
                            channel.getChannelNumber(), channel.getTemplateParam(), channel.getTemplateCode());
                    if (sendSmsResponse.getCode().equals("OK")) {
                        log.info("发送短信成功!");
                    } else {
                        log.info("发送短信失败：{}", sendSmsResponse.getMessage());
                    }
                } catch (Exception e) {
                    log.error("发送短信失败，phone={}", channel.getChannelNumber());
                }

            }
        }

    }

    @Override
    public void read(Long id) {
        LambdaUpdateWrapper<MessageInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MessageInfo::getReadFlag, 1)
                .eq(MessageInfo::getId, id);

        this.update(updateWrapper);
    }

    @Override
    public void handle(HandleMessageCmd cmd) {
        LambdaQueryWrapper<MessageInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageInfo::getBusinessId, cmd.getBusinessId())
                .eq(MessageInfo::getBusinessType, cmd.getBusinessType())
                .eq(cmd.getReceiveTo() != null, MessageInfo::getReceiveTo, cmd.getReceiveTo())
                .eq(MessageInfo::getHandleFlag, 0);

        List<MessageInfo> messageInfoList = baseMapper.selectList(queryWrapper);
        messageInfoList.forEach(messageInfo -> handleById(messageInfo.getId()));
    }

    /**
     * 将消息置为已办
     * PS：置为已办的消息同时将消息置为已读
     * @param id 消息ID
     */
    private void handleById(Long id) {
        LambdaUpdateWrapper<MessageInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MessageInfo::getId, id)
                .set(MessageInfo::getHandleDate, LocalDateTime.now())
                .set(MessageInfo::getHandleFlag, 1)
                .set(MessageInfo::getReadFlag, 1);

        baseMapper.update(null, updateWrapper);
    }

    @Override
    public void delete(DeleteMessageCmd cmd) {
        LambdaUpdateWrapper<MessageInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MessageInfo::getBusinessId, cmd.getBusinessId())
                .eq(MessageInfo::getBusinessType, cmd.getBusinessType());

        baseMapper.delete(updateWrapper);
    }
}
