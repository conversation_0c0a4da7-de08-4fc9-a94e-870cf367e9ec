package com.dh.message.service.impl;

import com.dh.message.entity.MessagePushTaskRecord;
import com.dh.message.mapper.MessagePushTaskRecordMapper;
import com.dh.message.service.MessagePushTaskRecordService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service("messagePushTaskRecordServiceImpl")
@AllArgsConstructor
public class MessagePushTaskRecordServiceImpl implements MessagePushTaskRecordService {

    private MessagePushTaskRecordMapper messagePushTaskRecordMapper;

    /**
     * @param messagePushTaskRecord
     * @return
     */
    @Override
    public int insertMessagePushTaskRecord(MessagePushTaskRecord messagePushTaskRecord) {
        return messagePushTaskRecordMapper.insert(messagePushTaskRecord);
    }

}
