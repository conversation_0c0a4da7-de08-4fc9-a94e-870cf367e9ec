package com.dh.message.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dh.message.entity.MessageAliyunSmsTemplate;
import com.dh.message.mapper.MessageAliyunSmsTemplateMapper;
import com.dh.message.service.IMessageAliyunSmsTemplateService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 阿里云短信模版信息 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2020-05-14
 */
@Service
public class MessageAliyunSmsTemplateServiceImpl extends ServiceImpl<MessageAliyunSmsTemplateMapper,
        MessageAliyunSmsTemplate> implements IMessageAliyunSmsTemplateService {

    @Override
    public List<MessageAliyunSmsTemplate> listForLoad() {
        return baseMapper.listForLoad();
    }
}
