package com.dh.message.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dh.common.constant.MessageTypeConstant;
import com.dh.common.util.R;
import com.dh.message.dto.SendMessageDTO;
import com.dh.message.entity.MessageInfo;
import com.dh.message.entity.MessageTemplate;
import com.dh.message.service.IMessageInfoService;
import com.dh.message.service.IMessageService;
import com.dh.message.service.IMessageTemplateService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@AllArgsConstructor
@Service
public class MessageServiceImpl implements IMessageService {

    private ObjectMapper objectMapper;

    private IMessageTemplateService messageTemplateService;

    private IMessageInfoService messageInfoService;

    /**
     * 发送消息
     * @param sendMessage 模板编码
     * @return
     */
    @Override
    public R sendMessage(SendMessageDTO sendMessage) {
        MessageTemplate template = messageTemplateService.getByTemplateCode(sendMessage.getMessageTemplateCode());
        if(template == null) {
            return R.failed(String.format("消息模板【%s】不存在或已删除", sendMessage.getMessageTemplateCode()));
        }
        if(CollectionUtils.isEmpty(sendMessage.getReceiveToList())) {
            return R.failed("消息接收人为空！");
        }
        String content = parseMessageContent(template.getTemplateContent(), sendMessage.getMessageTemplateParams());
        List<MessageInfo> messages = new ArrayList<>();
        sendMessage.getReceiveToList().forEach(r -> {
            MessageInfo messageInfo = new MessageInfo();
            messageInfo.setMessageTemplateId(template.getId());
            messageInfo.setSubject(template.getTemplateName());
            messageInfo.setContent(content);
            messageInfo.setMsgUrl(sendMessage.getBusinessUrl());
            if(!CollectionUtils.isEmpty(sendMessage.getBusinessUrlParams())) {
                try {
                    messageInfo.setParamsValue(objectMapper.writeValueAsString(sendMessage.getBusinessUrlParams()));
                } catch (JsonProcessingException e) {
                    log.error("发送消息时，消息url参数序列化异常！", e);
                }
            }
            messageInfo.setMsgType(MessageTypeConstant.MY_TASK);
            messageInfo.setReceiveTo(r);

            messages.add(messageInfo);
        });
        messageInfoService.saveBatch(messages);
        return R.ok();
    }

    /**
     * 设置消息读取状态
     * @param messageIds 消息ID集合
     * @param readFlag 是否已读（0：否，1：是）
     */
    @Override
    public void setRead(List<Long> messageIds, int readFlag) {
        LambdaUpdateWrapper<MessageInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MessageInfo::getReadFlag, readFlag)
                .in(MessageInfo::getId, messageIds);

        messageInfoService.update(updateWrapper);
    }

    /**
     * 解析消息模板内容
     * @param templateContent 消息模板内容
     * @param paramMap 消息模板参数
     * @return
     */
    private String parseMessageContent(String templateContent, Map<String, String> paramMap) {
        if(StringUtils.isEmpty(templateContent) || CollectionUtils.isEmpty(paramMap)) {
            return templateContent;
        }
        String[] result = { templateContent };
        paramMap.forEach((key, val) -> {
            String pattern = "\\$\\{" + key + "\\}";
            result[0] = result[0].replaceAll(pattern, val);
        });
        return result[0];
    }
}
