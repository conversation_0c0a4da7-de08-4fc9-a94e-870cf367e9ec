package com.dh.message.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dh.message.entity.MessageTemplate;
import com.dh.message.mapper.MessageTemplateMapper;
import com.dh.message.service.IMessageTemplateService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.Version;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.StringWriter;

/**
 * <p>
 * 消息模板 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2021-01-26
 */
@Service
@Slf4j
public class MessageTemplateServiceImpl extends ServiceImpl<MessageTemplateMapper, MessageTemplate> implements IMessageTemplateService {

    @Override
    public String getSubjectByTemplate(Long id, String paramsJson) {
        try {
            MessageTemplate messageTemplate = baseMapper.selectById(id);
            String templateContent = messageTemplate.getTemplateContent();
            JSONObject jsonObject = JSONObject.parseObject(paramsJson);
            Template template = new Template("strTpl", templateContent, new Configuration(new Version("2.3.23")));
            StringWriter result = new StringWriter();
            template.process(jsonObject, result);
            return result.toString();
        } catch (Exception e) {
            log.error("freemarker替换模板字符串报错，请检查参数是否正确,模板id=%s,参数=%s", id, paramsJson);
            return "ERROR";
        }
    }

    /**
     * 根据模板编码，查询模板信息
     * @param code
     * @return
     */
    @Override
    public MessageTemplate getByTemplateCode(String code) {
        LambdaQueryWrapper<MessageTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageTemplate:: getTemplateCode, code);
        return baseMapper.selectOne(queryWrapper);
    }

}
