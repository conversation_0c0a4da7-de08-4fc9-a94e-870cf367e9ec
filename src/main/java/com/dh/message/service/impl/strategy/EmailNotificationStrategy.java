package com.dh.message.service.impl.strategy;

import com.dh.common.util.R;
import com.dh.dto.bean.dto.messagerelay.SendEmailDTO;
import com.dh.dto.feign.MailFeignService;
import com.dh.message.bean.bo.AlarmLevelConfig;
import com.dh.message.bean.bo.NotificationConfig;
import com.dh.message.bean.bo.NotifyTarget;
import com.dh.message.bean.bo.NotifyTo;
import com.dh.message.dto.TaskMonitorInfoDTO;
import com.dh.message.enums.NotificationTypeEnum;
import com.dh.message.service.GroupNotificationStrategy;
import com.dh.message.service.ISysUserService;
import com.dh.message.util.TemplateRenderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.dh.message.service.impl.NotificationImpl.getTemplateParams;
import static com.dh.message.service.impl.NotificationImpl.objectToMap;

/**
 * 邮件通知策略实现
 *
 * <AUTHOR>
 * @version 0.9.1
 * @since 2025/7/25
 */
@Service
@Slf4j
public class EmailNotificationStrategy implements GroupNotificationStrategy {
    @Resource
    ISysUserService sysUserService;
    @Resource
    private MailFeignService mailFeignService;

    @Override
    public String getType() {
        return NotificationTypeEnum.TYPE_EMAIL.getName();
    }

    @Override
    @Async("sendPoolExecutor")
    public Future<Boolean> send(String template, AlarmLevelConfig alarmLevel, TaskMonitorInfoDTO taskMonitorInfoDTO, NotificationConfig notificationConfig) {
        try {
            //log.info("发送邮件通知: 模板={}, 目标={}", template, notifyTargets);
            Map<String, String> params = objectToMap(taskMonitorInfoDTO);
            List<Long> toUsers = new ArrayList<>();
            List<Long> ccUsers = new ArrayList<>();
            alarmLevel.getNotifyTo().forEach(notifyTarget -> {
                if (!notifyTarget.getField().isEmpty()) toUsers.addAll(notifyTarget.getUids(params));
                else ccUsers.addAll(notifyTarget.getUids(params));
            });

            if (toUsers.isEmpty()) {
                log.warn("邮件通知目标为空, 退出发送.");

                return AsyncResult.forValue(false);
            }

            List<String> ccUserEmails;
            if (ccUsers.isEmpty()) {
                log.warn("邮件通知抄送目标为空");
                ccUserEmails = new ArrayList<>();
            } else {
                ccUserEmails = sysUserService.getEmailsByUids(ccUsers).stream().filter(email -> email != null && !email.isEmpty()).collect(Collectors.toList());
            }

            List<String> toUserEmails = sysUserService.getEmailsByUids(toUsers).stream().filter(email -> email != null && !email.isEmpty()).collect(Collectors.toList());
            if (toUserEmails.isEmpty()) {
                log.warn("邮件通知目标邮箱为空, 退出发送.");

                return AsyncResult.forValue(false);
            }
            String subject = notificationConfig.getParams().getOrDefault("subject", "任务监控报警").toString();
            subject = TemplateRenderUtil.render(subject, getTemplateParams(taskMonitorInfoDTO, alarmLevel));

            SendEmailDTO sendEmailDTO = new SendEmailDTO();
            sendEmailDTO.setTableId(taskMonitorInfoDTO.getId());
            sendEmailDTO.setSubject(subject);
            sendEmailDTO.setText(template);
            sendEmailDTO.setTo(toUserEmails);
            sendEmailDTO.setCc(ccUserEmails);

            R<Boolean> res = mailFeignService.sendEmail(sendEmailDTO);
            log.info("发送邮件通知结果: {}, {}={}", res, toUserEmails, ccUserEmails);

            return AsyncResult.forValue(true);
        } catch (Exception e) {
            log.error("发送邮件通知失败: 模板={}, 目标={}", template, alarmLevel, e);
            return AsyncResult.forValue(false);
        }
    }

    @Override
    public boolean validate(List<NotifyTarget> notifyTarget) {
        if (notifyTarget == null) {
            log.warn("邮件通知列表为空");
            return false;
        }

        return true;
    }
} 