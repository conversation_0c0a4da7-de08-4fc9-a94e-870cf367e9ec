package com.dh.message.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dh.message.entity.MessagePush;
import com.dh.message.mapper.MessagePushMapper;
import com.dh.message.service.MessagePushService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("messagePushServiceImpl")
@AllArgsConstructor
public class MessagePushServiceImpl implements MessagePushService {

    private MessagePushMapper messagePushMapper;

    public MessagePush getMessagePush(String messagePushId) {
        return messagePushMapper.selectById(messagePushId);
    }

    public int insertMessagePush(MessagePush messagePush) {
        return messagePushMapper.insert(messagePush);
    }

    /**
     * 根据条件查询
     *
     * @param queryWrapper
     * @return
     */
    @Override
    public List<MessagePush> getMessagePushList(QueryWrapper<MessagePush> queryWrapper) {
        return messagePushMapper.selectList(queryWrapper);
    }

    /**
     * 更新推送消息 (多个)
     *
     * @param messagePushList
     * @return
     */
    @Override
    public void updateMessagePushList(List<MessagePush> messagePushList) {
        for (MessagePush messagePush : messagePushList) {
            messagePushMapper.updateById(messagePush);
        }
    }

    /**
     * 更新推送消息 （单个）
     *
     * @param messagePush
     * @return
     */
    @Override
    public boolean updateMessagePush(MessagePush messagePush) {
        int result = messagePushMapper.updateById(messagePush);
        return result > 0;
    }

    /**
     * 删除推送消息
     *
     * @param idList
     * @return
     */
    @Override
    public boolean deleteMessagePush(List<String> idList) {
        int result = messagePushMapper.deleteBatchIds(idList);
        return result == idList.size();
    }
}
