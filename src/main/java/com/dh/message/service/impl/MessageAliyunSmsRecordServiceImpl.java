package com.dh.message.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dh.message.entity.MessageAliyunSmsRecord;
import com.dh.message.mapper.MessageAliyunSmsRecordMapper;
import com.dh.message.service.IMessageAliyunSmsRecordService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 阿里云短信发送记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-09
 */
@Service
public class MessageAliyunSmsRecordServiceImpl extends ServiceImpl<MessageAliyunSmsRecordMapper,
        MessageAliyunSmsRecord> implements IMessageAliyunSmsRecordService {

}
