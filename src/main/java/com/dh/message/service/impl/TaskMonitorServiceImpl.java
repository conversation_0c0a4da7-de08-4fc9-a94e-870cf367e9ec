package com.dh.message.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dh.common.constant.LanguageConstant;
import com.dh.common.util.R;
import com.dh.common.util.WorkdayUtil;
import com.dh.dto.bean.dto.message.GetLastRunningTaskMonitorDTO;
import com.dh.dto.bean.dto.message.SubmitTaskMonitorDTO;
import com.dh.dto.bean.dto.message.SubmitTaskMonitorResDTO;
import com.dh.dto.bean.dto.message.UpdateTaskMonitorDTO;
import com.dh.dto.bean.dto.messagerelay.ToPushUserIdDTO;
import com.dh.dto.enums.WeComEnum;
import com.dh.dto.feign.WeComFeignService;
import com.dh.framework.security.exception.BusinessException;
import com.dh.message.bean.bo.AlarmLevelConfig;
import com.dh.message.bean.bo.RelatedNotificationConfig;
import com.dh.message.bean.entity.TaskMonitor;
import com.dh.message.bean.entity.TaskMonitorHistory;
import com.dh.message.bean.entity.TaskMonitorSetting;
import com.dh.message.bean.fm.TaskMonitorFM;
import com.dh.message.bean.vo.TaskMonitorVO;
import com.dh.message.constant.AlertFlagConstant;
import com.dh.message.constant.SystemConstant;
import com.dh.message.controller.vo.HandleMessageCmd;
import com.dh.message.dto.TaskMonitorInfoDTO;
import com.dh.message.dto.TriggerAlertDTO;
import com.dh.message.enums.LevelUnitEnum;
import com.dh.message.enums.TaskMonitorSettingTypeEnum;
import com.dh.message.enums.TaskMonitorStatusEnum;
import com.dh.message.listener.TaskMonitorEventPublisher;
import com.dh.message.mapper.TaskMonitorMapper;
import com.dh.message.service.*;
import com.dh.message.service.check.CheckFactory;
import com.dh.message.service.check.TaskMonitorCheck;
import com.dh.message.util.TemplateRenderUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.dh.message.enums.TaskMonitorHistoryTypeEnum.UpdateStatusType;

/**
 * <p>
 * 任务监控表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@Service
public class TaskMonitorServiceImpl extends ServiceImpl<TaskMonitorMapper, TaskMonitor> implements TaskMonitorService {
    @Resource
    private TaskMonitorEventPublisher publisher;

    @Resource
    private CheckFactory checkFactory;

    @Resource
    private TaskMonitorMapper taskMonitorMapper;

    @Resource
    private TaskMonitorSettingService taskMonitorSettingService;

    @Resource
    private TaskMonitorHistoryService taskMonitorHistoryService;

    @Resource
    private WeComFeignService weComFeignService;

    @Resource
    private TaskMonitorEventPublisher taskMonitorEventPublisher;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private IMessageInfoService messageInfoService;

    @Resource
    private NotificationStrategyFactory strategyFactory;

    @Resource
    private ThreadPoolExecutor threadPoolExecutor;

    @Resource
    private TaskMonitorService taskMonitorService;

    @Resource
    private ISysUserService sysUserService;

    /**
     * 分页获得进行中的任务
     *
     * @param pageSize 页码大小
     * @param pageNum  页码
     * @return 进行中的任务
     */
    public List<TaskMonitorInfoDTO> getInProgressTask(int pageSize, int pageNum) {
        return taskMonitorMapper.getInProgressTaskByBatch(pageSize, pageNum);
    }

    /**
     * 获得进行中任务数量
     *
     * @return 进行中的任务数量
     */
    public long getInProgressTaskCount() {
        return taskMonitorMapper.getInProgressTaskCount();
    }

    /**
     * 检测任务状态, 发生异常的进行发送通知等基本操作
     *
     * @param taskMonitorInfoDTO 任务监控的基本信息`````
     * <AUTHOR>
     * @since 2025/07/24 18:00
     */
    @Override
    public void checkTask(TaskMonitorInfoDTO taskMonitorInfoDTO) {
        //log.debug("TaskMonitorService check {} ID: {}, flag: {}", taskMonitorInfoDTO.getTitle(),
        //        taskMonitorInfoDTO.getId(),
        //        taskMonitorInfoDTO.getAlertFlag());

        TaskMonitorCheck taskMonitorCheck = checkFactory.create(taskMonitorInfoDTO.getType());
        taskMonitorCheck.check(taskMonitorInfoDTO);
    }

    /**
     * 发起任务监控
     *
     * @param submitTaskMonitorDTO 提交任务监控信息
     * @Author: zgj
     * @Date: 2023-06-08 20:45
     * @return: java.lang.Long
     **/
    @Override
    public SubmitTaskMonitorResDTO submitTaskMonitor(SubmitTaskMonitorDTO submitTaskMonitorDTO) {
        TaskMonitor taskMonitor = new TaskMonitor();
        TaskMonitorSetting taskMonitorSetting = taskMonitorSettingService.lambdaQuery()
                .eq(TaskMonitorSetting::getBusinessType, submitTaskMonitorDTO.getTaskMonitorSettingBusinessType()).one();
        if (submitTaskMonitorDTO.getEndDateTime() == null && submitTaskMonitorDTO.getEndDate() == null) {
            throw new BusinessException("监控任务不能没有结束时间！",
                    "Monitoring tasks cannot do without an end time!");
        }
        if (taskMonitorSetting == null) {
            throw new BusinessException(
                    String.format("%s任务监控配置不存在！", submitTaskMonitorDTO.getTaskMonitorSettingBusinessType()),
                    String.format("%s task monitoring configuration does not exist!", submitTaskMonitorDTO.getTaskMonitorSettingBusinessType())
            );
        }
        taskMonitor.setTmsId(taskMonitorSetting.getId());
        if (!StringUtils.isEmpty(submitTaskMonitorDTO.getBusinessType())) {
            taskMonitor.setBusinessType(submitTaskMonitorDTO.getBusinessType());
        } else {
            taskMonitor.setBusinessType("");
        }
        taskMonitor.setBusinessId(submitTaskMonitorDTO.getBusinessId());
        if (submitTaskMonitorDTO.getStartDate() == null) {
            taskMonitor.setStartDate(LocalDateTime.now());
        } else {
            taskMonitor.setStartDate(submitTaskMonitorDTO.getStartDate());
        }
        // 实际结束时间应该是结束时间加1天
        if (submitTaskMonitorDTO.getEndDateTime() != null) {
            submitTaskMonitorDTO.setEndDateTime(submitTaskMonitorDTO.getEndDateTime());
        }else {
            taskMonitor.setEndDate(submitTaskMonitorDTO.getEndDate().plusDays(1).atTime(LocalTime.MIN));
        }
        taskMonitor.setExtParams(submitTaskMonitorDTO.getParamMap());
        List<String> relatedNameList = sysUserService.getUserNameListById(submitTaskMonitorDTO.getUserIdList());
        if (!CollectionUtils.isEmpty(relatedNameList)) {
            taskMonitor.setRelatedName(String.join(",", relatedNameList));
        }
        taskMonitor.setStatus(TaskMonitorStatusEnum.STATUS_RUNNING.getCode());
        taskMonitor.setRelatedUid(submitTaskMonitorDTO.getUserIdList().stream()
                .map(String::valueOf)
                .collect(Collectors.joining(",")));

        String template = TemplateRenderUtil.render(taskMonitorSetting.getRelatedNotification().getTaskTemplate(),
                taskMonitor.gainExtParam());
        taskMonitor.setTaskContent(template);

        if (!CollectionUtils.isEmpty(submitTaskMonitorDTO.getProjectManagerIdList())) {
            taskMonitor.setProjectManagerUid(submitTaskMonitorDTO.getProjectManagerIdList().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")));
        }

        if (submitTaskMonitorDTO.getDeptHeadId() == null) {
            // 如果没有部门负责人，则获取责任人的部门负责人
            List<Long> deptHeadIdList = baseMapper.getDeptHeadId(submitTaskMonitorDTO.getUserIdList());
            if (!CollectionUtils.isEmpty(deptHeadIdList)) {
                taskMonitor.setDeptManagerUid(deptHeadIdList.get(0));
            }
        }

        if (submitTaskMonitorDTO.getDeptHeadId() != null) {
            taskMonitor.setDeptManagerUid(submitTaskMonitorDTO.getDeptHeadId());
        }

        // 计算日期
        this.calculateDate(taskMonitor, taskMonitorSetting);
        // 保存
        this.save(taskMonitor);
        // 发起任务提醒
        this.submitReminder(taskMonitor, taskMonitorSetting);

        // 添加任务日志
        this.addTaskMonitorHistory(taskMonitor);
        SubmitTaskMonitorResDTO submitTaskMonitorResDTO = new SubmitTaskMonitorResDTO();
        BeanUtils.copyProperties(taskMonitor, submitTaskMonitorResDTO);
        return submitTaskMonitorResDTO;

    }

    /**
     * 添加任务日志
     *
     * @param taskMonitor 任务监控
     * @Author: zgj
     * @Date: 2025/7/26 下午6:22
     * @return: void
     **/
    private void addTaskMonitorHistory(TaskMonitor taskMonitor) {
        // 添加任务日志
        TaskMonitorHistory taskMonitorHistory = new TaskMonitorHistory();
        taskMonitorHistory.setTmId(taskMonitor.getId());
        taskMonitorHistory.setTmsId(taskMonitor.getTmsId());
        // 添加任务日志
        // 类型是创建任务
        taskMonitorHistory.setType(UpdateStatusType.getCode());
        try {
            taskMonitorHistory.setUpdateInfo(objectMapper.writeValueAsString(taskMonitor));
        } catch (JsonProcessingException e) {
            log.error("{}数据json转换异常", taskMonitor, e);
        }
        taskMonitorHistoryService.save(taskMonitorHistory);
    }

    /**
     * 更新任务状态
     *
     * @param updateTaskMonitorDTO 更新任务状态参数
     * @Author: zgj
     * @Date: 2025/7/26 下午6:23
     * @return: java.lang.Long
     **/
    @Override
    public Long updateTaskMonitorProgress(UpdateTaskMonitorDTO updateTaskMonitorDTO) {
        switch (updateTaskMonitorDTO.getOperateType()) {
            case CANCEL: {
                // 先更新任务状态
                this.endTaskMonitor(updateTaskMonitorDTO, TaskMonitorStatusEnum.STATUS_CANCEL);
                break;
            }
            case DELETE: {
                break;
            }
            case COMPLETE: {
                // 先更新任务状态
                this.endTaskMonitor(updateTaskMonitorDTO, TaskMonitorStatusEnum.STATUS_FINISHED);
                break;
            }
            default:
                break;
        }
        return updateTaskMonitorDTO.getTaskMonitorId();
    }

    /**
     * 结束任务
     *
     * @param updateTaskMonitorDTO 更新任务信息
     * @param updateType    更新状态
     * @Author: zgj
     * @Date: 2025/7/25 下午7:52
     * @return: void
     **/
    public void endTaskMonitor(UpdateTaskMonitorDTO updateTaskMonitorDTO, TaskMonitorStatusEnum updateType) {
        List<TaskMonitorInfoDTO> taskMonitorList = baseMapper.getRunningTaskMonitorList(updateTaskMonitorDTO);
        if (CollectionUtils.isEmpty(taskMonitorList)) {
            return;
        }
        List<TaskMonitor> updateTaskMonitorList = new ArrayList<>();
        Map<String, Set<Long>> remindMap = new HashMap<>();
        WeComEnum weComEnum = WeComEnum.DEFAULT;
        for (TaskMonitorInfoDTO taskMonitorInfoDTO : taskMonitorList) {
            // 获取对象
            TaskMonitor taskMonitor = new TaskMonitor();
            BeanUtils.copyProperties(taskMonitorInfoDTO, taskMonitor);
            taskMonitor.setStatus(updateType.getCode());
            updateTaskMonitorList.add(taskMonitor);

            // 添加提醒内容
            String language = taskMonitorInfoDTO.getRelatedNotification().getLanguage();
            String remind = taskMonitor.getTaskContent() + this.getRemindSuffix(language, updateType);
            if (remindMap.containsKey(remind)) {
                // 有值，就加入
                remindMap.get(remind).addAll(taskMonitor.getUserIdList());
            }else {
                // 没值，则添加
                remindMap.put(remind, new HashSet<>(taskMonitor.getUserIdList()));
            }
            weComEnum = WeComEnum.valueOf(taskMonitorInfoDTO.getRelatedNotification().getWeComAppName());

            // 更新日志
            CompletableFuture.runAsync(() -> {
                        this.addTaskMonitorHistory(taskMonitor);
                        // 如果入参有责任人，则添加上责任人消代办
                        if (updateTaskMonitorDTO.getUserId() != null) {
                            for (Long userId : taskMonitor.getUserIdList()) {
                                HandleMessageCmd handleMessageCmd = new HandleMessageCmd();
                                handleMessageCmd.setBusinessId(taskMonitor.getBusinessId().toString());
                                handleMessageCmd.setBusinessType(taskMonitor.getBusinessType());
                                handleMessageCmd.setReceiveTo(userId);
                                messageInfoService.handle(handleMessageCmd);
                            }
                        } else {
                            // 如果没有责任人，则根据业务类型直接消除代办
                            HandleMessageCmd handleMessageCmd = new HandleMessageCmd();
                            handleMessageCmd.setBusinessId(taskMonitor.getBusinessId().toString());
                            handleMessageCmd.setBusinessType(taskMonitor.getBusinessType());
                            messageInfoService.handle(handleMessageCmd);
                        }

                    }, threadPoolExecutor)
                    .exceptionally(e -> {
                        log.error("{}写入任务报警日志异常", taskMonitor, e);
                        return null;
                    });
        }
        // 获取对象
        taskMonitorService.saveOrUpdateBatch(updateTaskMonitorList);
        for (Map.Entry<String, Set<Long>> stringSetEntry : remindMap.entrySet()) {
            String key = stringSetEntry.getKey();
            Set<Long> value = stringSetEntry.getValue();
            R<Long> res = weComFeignService.toPushUserId(new ToPushUserIdDTO(new ArrayList<>(value),
                    key,
                    weComEnum)
            );
            if (!res.isOk()) {
                log.error("发送任务发起提醒异常，异常信息{}", res.getMsg());
            }
        }
    }

    /**
     * 获取提醒后缀
     *
     * @param language   语言
     * @param updateType 更新类型
     * @Author: zgj
     * @Date: 2025/7/29 上午10:07
     * @return: java.lang.String
     **/
    private String getRemindSuffix(String language, TaskMonitorStatusEnum updateType) {
        return language.equals(LanguageConstant.LANGUAGE_CN) ? "任务" + updateType.getName() + "！"
                : " Task " + updateType.getNameEn() + "!";
    }

    /**
     * 提交任务发起提醒
     *
     * @param taskMonitor 提交任务发起提醒
     * @Author: zgj
     * @Date: 2025/7/25 下午4:17
     * @return: void
     **/
    private void submitReminder(TaskMonitor taskMonitor, TaskMonitorSetting taskMonitorSetting) {
        Integer maxPriorityLevel = null;
        for (Map.Entry<String, LocalDateTime> entry : taskMonitor.getDateNodes().entrySet()) {
            String key = entry.getKey();
            LocalDateTime value = entry.getValue();
            if (key.startsWith(SystemConstant.ALERT_PREFIX) && value == null) {
                //如果是报警节点，并且没有设置时间，则赋值
                int currentLevel = Integer.parseInt(key.replace(SystemConstant.ALERT_PREFIX, ""));
                if (maxPriorityLevel == null) {
                    maxPriorityLevel = currentLevel;
                } else {
                    // 如果已经有了报警节点，则比较优先级
                    if (maxPriorityLevel > currentLevel) {
                        maxPriorityLevel = currentLevel;
                    }
                }
            }
        }
        TaskMonitorInfoDTO taskMonitorInfoDTO = baseMapper
                .getUnfinishedTask(taskMonitor.getStatus(), taskMonitor.getId()).get(0);
        // 提交任务的消息
        this.sendTaskMonitorMessage(taskMonitor, taskMonitorSetting,
                taskMonitor.getUserIdList(), taskMonitorInfoDTO);
        if (maxPriorityLevel != null) {
            // 有值则触发报警
            taskMonitorEventPublisher.triggerAlert(TriggerAlertDTO.builder().isSkip(false)
                    .alertFlag(
                            AlertFlagConstant.getLevelFlag(maxPriorityLevel, false)
                    )
                    .isWarning(false)
                    .level(maxPriorityLevel).taskMonitorInfoDTO(taskMonitorInfoDTO).build());
        }
    }

    /**
     * 发送任务消息
     *
     * @param taskMonitor        消息模板
     * @param taskMonitorSetting 任务配置
     * @param userIdList         用户Id集合
     * @param taskMonitorInfoDTO 任务内容
     * @Author: zgj
     * @Date: 2025/7/28 上午11:25
     * @return: void
     **/
    private void sendTaskMonitorMessage(TaskMonitor taskMonitor, TaskMonitorSetting taskMonitorSetting,
                                        List<Long> userIdList, TaskMonitorInfoDTO taskMonitorInfoDTO) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }
        RelatedNotificationConfig relatedNotification = taskMonitorSetting.getRelatedNotification();
        List<RelatedNotificationConfig.RemindConfig> notificationList = relatedNotification.getRelatedNotifications();
        for (RelatedNotificationConfig.RemindConfig notificationConfig : notificationList) {
            String notificationType = notificationConfig.getType().toLowerCase();

            // 检查是否支持该通知类型
            if (!strategyFactory.supports(notificationType)) {
                log.warn("未实现的通知类型: {}", notificationType);
                return;
            }

            // 获取对应的策略
            NotificationStrategy strategy = strategyFactory.getStrategy(notificationType);
            String template = TemplateRenderUtil.render(notificationConfig.getTemplate(), taskMonitor.gainExtParam());

            // 发送通知
            for (Long userId : userIdList) {
                strategy.send(template, userId, taskMonitorInfoDTO, notificationConfig);
            }
        }
    }

    /**
     * 计算任务的开始时间和结束时间
     *
     * @param taskMonitor        任务数据
     * @param taskMonitorSetting 任务配置数据
     * @Author: zgj
     * @Date: 2025/7/25 下午3:22
     * @return: void
     **/
    private void calculateDate(TaskMonitor taskMonitor, TaskMonitorSetting taskMonitorSetting) {
        if (taskMonitorSetting.getType() == TaskMonitorSettingTypeEnum.LevelType.getCode()) {
            // 如果是分级的任务
            List<AlarmLevelConfig> alarmLevelList = taskMonitorSetting.getAlarmLevels();
            Map<String, LocalDateTime> dateNodes = new HashMap<>();
            for (AlarmLevelConfig alarmLevel : alarmLevelList) {
                // key = 前缀 + level
                // 报警key
                String key = SystemConstant.ALERT_PREFIX + alarmLevel.getLevel();
                // 计算报警时间 = 完成时间 - 天数偏移
                LocalDateTime alarmDate = this.getAlarmDate(taskMonitor.getEndDate(), alarmLevel.getDay(),
                        alarmLevel.getUnit());
                if (alarmDate != null && alarmDate.isAfter(taskMonitor.getStartDate())) {
                    // 如果报警时间在开始时间之后，则添加到日期节点中
                    dateNodes.put(key, alarmDate);
                    if (!alarmLevel.getUnit().equals(LevelUnitEnum.HOURS.name())) {
                        // 如果有报警时间并且不是按小时算的，那就算一下预警时间
                        // 预警时间 = 报警时间 - 1天 的12点
                        LocalDateTime warningDate = alarmDate.minusDays(1)
                                .withHour(12).withMinute(0).withSecond(0).withNano(0);
                        if (warningDate.isAfter(taskMonitor.getStartDate())) {
                            // 如果预警时间在开始时间之后，那就提醒
                            dateNodes.put(SystemConstant.WARNING_PREFIX + alarmLevel.getLevel(), warningDate);
                        } else {
                            dateNodes.put(SystemConstant.WARNING_PREFIX + alarmLevel.getLevel(), null);
                        }
                    } else {
                        // 没有就填null
                        dateNodes.put(SystemConstant.WARNING_PREFIX + alarmLevel.getLevel(), null);
                    }
                }else {
                    dateNodes.put(key, null);
                }
            }
            taskMonitor.setDateNodes(dateNodes);
        }
    }

    /**
     * 计算报警时间
     *
     * @param endDate 结束时间
     * @param day     天数
     * @Author: zgj
     * @Date: 2025/7/26 下午6:23
     * @return: java.time.LocalDateTime
     **/
    private LocalDateTime getAlarmDate(LocalDateTime endDate, Integer day,
                                       String unit) {
        LocalDateTime date;
        if (unit.equals(LevelUnitEnum.WORK_DAYS.name())) {
            // 如果是工作日
            // 先减一天
            date = endDate.minusDays(1);
            // 然后加指定工作日
            date = WorkdayUtil.addWorkingDays(date, -day);
            // 然后在加1天
            date = date.plusDays(1);
        }else {
            date = endDate.minus(day, ChronoUnit.valueOf(unit));
        }
        if (unit.equals(LevelUnitEnum.HOURS.name())) {
            // 如果是小时，则直接返回
            return date;
        }
        // 报警时间完成时间早上9点
        date = date.withHour(9).withMinute(0).withSecond(0).withNano(0);
        return date;
    }

    @Override
    public List<TaskMonitorVO> getTaskMonitorList(TaskMonitorFM fm) {
        return baseMapper.getTaskMonitorList(fm);
    }

    /**
     * 获取最近一次进行中的任务监控数据
     * @Author: zgj
     * @Date: 2023-06-08 20:45
     * @param dto 入参
     * @return: com.dh.common.util.R<com.dh.dto.bean.dto.message.SubmitTaskMonitorResDTO>
     **/
    @Override
    public SubmitTaskMonitorResDTO getLastRunningTaskMonitor(GetLastRunningTaskMonitorDTO dto) {
        return baseMapper.getLastRunningTaskMonitor(dto);
    }


}
