package com.dh.message.service.impl;


import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dh.message.entity.DhMessageUrl;
import com.dh.message.mapper.DhMessageUrlMapper;
import com.dh.message.service.DhMessageUrlService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 非点击消除代办URL配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Service
public class DhMessageUrlServiceImpl extends ServiceImpl<DhMessageUrlMapper, DhMessageUrl> implements DhMessageUrlService {
    @Override
    public boolean queryUrl(String msgUrl) {
        return new LambdaQueryChainWrapper<>(baseMapper).eq(DhMessageUrl::getMsgUrl,msgUrl).count()==0;
    }
}
