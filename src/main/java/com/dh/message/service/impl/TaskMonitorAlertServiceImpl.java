package com.dh.message.service.impl;

import com.dh.message.bean.entity.TaskMonitorAlert;
import com.dh.message.dto.TriggerAlertDTO;
import com.dh.message.mapper.TaskMonitorAlertMapper;
import com.dh.message.service.TaskMonitorAlertService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 任务监控报警表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Service
@Slf4j
public class TaskMonitorAlertServiceImpl extends ServiceImpl<TaskMonitorAlertMapper, TaskMonitorAlert> implements TaskMonitorAlertService {

    @Override
    public TaskMonitorAlert add(TriggerAlertDTO triggerAlertDTO, List<Long> ccUsers) {
        TaskMonitorAlert taskMonitorAlert = new TaskMonitorAlert();
        taskMonitorAlert.setIsWarning(triggerAlertDTO.getIsWarning() ? 1 : 0);
        taskMonitorAlert.setTmsId(triggerAlertDTO.getTaskMonitorInfoDTO().getTmsId());
        taskMonitorAlert.setTmId(triggerAlertDTO.getTaskMonitorInfoDTO().getId());
        taskMonitorAlert.setAlertLevel(triggerAlertDTO.getLevel());
        taskMonitorAlert.setToUserId(triggerAlertDTO.getTaskMonitorInfoDTO().getRelatedUid());
        taskMonitorAlert.setToCcUsers(Strings.join(ccUsers, ','));
        taskMonitorAlert.setMessage("任务发生预警");

        this.save(taskMonitorAlert);
        log.info("添加任务监控报警: {}", taskMonitorAlert.getId());
        return taskMonitorAlert;
    }
}
