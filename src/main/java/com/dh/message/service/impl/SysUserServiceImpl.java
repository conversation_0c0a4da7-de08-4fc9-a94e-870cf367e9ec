package com.dh.message.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dh.message.entity.SysUser;
import com.dh.message.mapper.SysUserMapper;
import com.dh.message.service.ISysUserService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 系统用户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-8-4
 */
@AllArgsConstructor
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {

    @Override
    public List<String> getEmailsByUids(List<Long> uids) {
        // 对输入的uids去重，然后查询邮箱，最后对返回的邮箱列表也去重
        return this.baseMapper.getEmailsByUids(uids.stream().distinct().collect(Collectors.toList()))
                .stream()
                .filter(email -> email != null && !email.trim().isEmpty()) // 过滤空邮箱
                .distinct() // 对返回的邮箱去重
                .collect(Collectors.toList());
    }

    /**
     * 根据用户ID列表查询用户名称列表
     * @Author: zgj
     * @Date: 2025/8/6 下午8:32
     * @param userIdList 用户ID列表
     * @return: java.util.List<java.lang.String>
     **/
    @Override
    public List<String> getUserNameListById(List<Long> userIdList) {
        return baseMapper.getUserNameListById(userIdList);
    }
}
