package com.dh.message.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.dh.message.entity.DhMessageUrl;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 非点击消除代办URL配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
public interface DhMessageUrlService extends IService<DhMessageUrl> {

    /**
     * 查询是否是非点击消除的代办
     *
     * @param msgUrl 代办链接
     * @return 是否是非点击消除的代办
     */
    boolean queryUrl(@Param("msgUrl") String msgUrl);
}