package com.dh.message.service;

import com.dh.message.bean.bo.*;
import com.dh.message.dto.TaskMonitorInfoDTO;

import java.util.List;
import java.util.concurrent.Future;

/**
 * 通知组策略接口
 * 用于实现不同通知组类型的发送策略
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
public interface GroupNotificationStrategy {
    /**
     * 获取策略类型
     */
    String getType();

    /**
     * 发送通知
     *
     * @param template           通知模板
     * @param alarmLevel         通知级别
     * @param taskMonitorInfoDTO TaskMonitorInfoDTO
     * @return 是否发送成功
     */
    Future<Boolean> send(String template, AlarmLevelConfig alarmLevel, TaskMonitorInfoDTO taskMonitorInfoDTO,
                         NotificationConfig notificationConfig);

    /**
     * 验证通知参数
     *
     * @param notifyTarget 通知目标
     */
    boolean validate(List<NotifyTarget> notifyTarget);
} 