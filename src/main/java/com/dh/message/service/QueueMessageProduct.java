package com.dh.message.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;
import com.dh.message.config.RabbitMQConfig;
import javax.annotation.Resource;

@Component
@Slf4j
public class QueueMessageProduct<T> {
    @Resource
    private RabbitTemplate rabbitTemplate;

    public void send(String channel, T data) {
        String routingKey = channel + ".routing";
        log.info("Send queue message: routingKey={}, data={}", routingKey, data);

        rabbitTemplate.convertAndSend(RabbitMQConfig.TASK_MONITOR_HOOK_EXCHANGE, routingKey, data);
    }
}
