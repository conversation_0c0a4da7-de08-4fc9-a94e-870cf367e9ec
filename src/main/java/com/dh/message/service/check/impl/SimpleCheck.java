package com.dh.message.service.check.impl;

import com.dh.message.enums.TaskMonitorSettingTypeEnum;
import com.dh.message.dto.TaskMonitorInfoDTO;
import com.dh.message.service.check.TaskMonitorCheck;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 简单任务检测
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Service("simpleCheck")
@Slf4j
public class SimpleCheck implements TaskMonitorCheck {
    @Override
    public void check(TaskMonitorInfoDTO taskMonitorInfoDTO) {
        log.info("SimpleCheck");
    }

    @Override
    public Integer getType() {
        return TaskMonitorSettingTypeEnum.SimpleType.getCode();
    }
}
