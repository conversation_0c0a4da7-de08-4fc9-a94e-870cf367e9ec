package com.dh.message.service.check;

import com.dh.framework.security.exception.BusinessException;
import com.dh.message.dto.TaskMonitorInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class CheckFactory {
    private final List<TaskMonitorCheck> handlers;
    private Map<Integer, TaskMonitorCheck> checkFuncs;

    @Autowired
    public CheckFactory(List<TaskMonitorCheck> handlers) {
        this.handlers = handlers;
    }

    @PostConstruct
    public void init() {
        checkFuncs = handlers.stream().collect(Collectors.toMap(TaskMonitorCheck::getType, Function.identity()));
    }

    /**
     * 任务检测创建工厂
     *
     * @param type 类型
     * @return DeadlineCalc
     */
    public TaskMonitorCheck create(int type) {
        TaskMonitorCheck taskMonitorCheck = checkFuncs.get(type);

        if (taskMonitorCheck == null) {
            throw new BusinessException("不支持的检测类型: " + type);
        }

        return taskMonitorCheck;
    }
}
