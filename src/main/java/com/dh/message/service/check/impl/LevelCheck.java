package com.dh.message.service.check.impl;

import com.dh.message.bean.entity.TaskMonitor;
import com.dh.message.constant.AlertFlagConstant;
import com.dh.message.constant.SystemConstant;
import com.dh.message.enums.TaskMonitorSettingTypeEnum;
import com.dh.message.dto.TaskMonitorInfoDTO;
import com.dh.message.dto.TriggerAlertDTO;
import com.dh.message.enums.TaskMonitorStatusEnum;
import com.dh.message.listener.TaskMonitorEventPublisher;
import com.dh.message.service.TaskMonitorService;
import com.dh.message.service.check.TaskMonitorCheck;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;


/**
 * 分级任务监测检查
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Service("levelCheck")
@Slf4j
public class LevelCheck implements TaskMonitorCheck {
    @Resource
    private TaskMonitorEventPublisher taskMonitorEventPublisher;

    @Resource
    private TaskMonitorService taskMonitorService;

    /**
     * 按等级检查任务
     *
     * @param taskMonitorInfoDTO 任务信息
     * @param level              等级
     * @param isWarning          是否预警
     * @return 是否通过
     */
    private boolean checkLevel(TaskMonitorInfoDTO taskMonitorInfoDTO, int level, boolean isWarning) {
        int alertFlag = taskMonitorInfoDTO.getAlertFlag();
        int lvFlag = AlertFlagConstant.getLevelFlag(level, isWarning);  //位操作获取

        if ((alertFlag & lvFlag) == 0) {    //已经发送过
            String key = (isWarning ? SystemConstant.WARNING_PREFIX : SystemConstant.ALERT_PREFIX) + level;
            if (taskMonitorInfoDTO.getDateNodes() == null) {
                log.warn("异常数据: {}, 没有计算DateNode", taskMonitorInfoDTO.getId());
                return false;
            }

            LocalDateTime time = taskMonitorInfoDTO.getDateNodes().getOrDefault(key, null);
            LocalDateTime endDate = taskMonitorInfoDTO.getEndDate();

            if (endDate.isBefore(LocalDateTime.now())) {    //更新任务状态为逾期
                if (taskMonitorInfoDTO.getStatus() == TaskMonitorStatusEnum.STATUS_RUNNING.getCode()) {
                    log.info("更新Task monitor {} 状态为已逾期", taskMonitorInfoDTO.getId());

                    TaskMonitor taskMonitor = new TaskMonitor();
                    taskMonitor.setId(taskMonitorInfoDTO.getId());
                    taskMonitor.setStatus(TaskMonitorStatusEnum.STATUS_OVERDUE.getCode());

                    taskMonitorService.updateById(taskMonitor);
                }
            }

            //log.info("level {}, isWarning: {}, key: {} time: {}", level, isWarning, key, time);
            int newFlag = taskMonitorInfoDTO.getAlertFlag() | lvFlag;

            if (time != null) {
                if (time.isBefore(LocalDateTime.now())) {
                    log.info("Task {}, level {},{} task time {} is finished ", taskMonitorInfoDTO.getId(), level, isWarning, time);

                    taskMonitorEventPublisher.triggerAlert(TriggerAlertDTO.builder().isSkip(false).alertFlag(newFlag).isWarning(isWarning).level(level).taskMonitorInfoDTO(taskMonitorInfoDTO).build());

                    return true;
                } else {
                    //log.debug("Task {}, level {},{} task time {} is not finished ", taskMonitorInfoDTO.getId(), level, isWarning, time);

                    return false;
                }
            } else {
                //如果时间字段内容为empty 直接跳过, 记录新的标志值
                log.info("Task {}, level {} is check date is null warning update flag.", taskMonitorInfoDTO.getId(),
                        level);

                taskMonitorEventPublisher.triggerAlert(TriggerAlertDTO.builder().isSkip(true).alertFlag(newFlag).isWarning(isWarning).level(level).taskMonitorInfoDTO(taskMonitorInfoDTO).build());
                return true;
            }
        } else {
            return false;
        }
    }

    /**
     * 检测任务是否超时
     *
     * @param taskMonitorInfoDTO 任务信息
     */
    @Override
    public void check(TaskMonitorInfoDTO taskMonitorInfoDTO) {
        int maxAlertLevel = taskMonitorInfoDTO.getAlarmLevels().size();
        if (maxAlertLevel > 0) {
            for (int level = maxAlertLevel; level > 0; level--) {
                if (checkLevel(taskMonitorInfoDTO, level, true)) {
                    break;
                }

                if (checkLevel(taskMonitorInfoDTO, level, false)) {
                    break;
                }
            }
        }
    }

    @Override
    public Integer getType() {
        return TaskMonitorSettingTypeEnum.LevelType.getCode();
    }
}
