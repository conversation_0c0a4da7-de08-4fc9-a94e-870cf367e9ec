package com.dh.message.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dh.message.bean.entity.TaskMonitorSetting;
import com.dh.message.bean.fm.TaskMonitorSettingFM;
import com.dh.message.bean.fm.TaskMonitorSettingQueryFM;
import com.dh.message.bean.vo.TaskMonitorSettingCardVO;
import com.dh.message.bean.vo.TaskMonitorSettingVO;

import java.util.List;

/**
 * <p>
 * 任务监控配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface TaskMonitorSettingService extends IService<TaskMonitorSetting> {

    /**
     * 保存或更新任务监控配置
     * @Author: zy
     * @Date: 2025/7/25 11:10
     * @param fm 任务监控配置前端传参对象
     * @return: java.lang.Long id
     **/
    Long saveOrUpdateTaskMonitorSetting(TaskMonitorSettingFM fm);

    /**
     * 根据ID查询任务监控配置详情
     * @Author: zy
     * @Date: 2025/7/25 11:08
     * @param id id 主键ID
     * @return: com.dh.message.bean.vo.TaskMonitorSettingVO 任务监控配置VO
     **/
    TaskMonitorSettingVO getTaskMonitorSettingDetail(Long id);

    /**
     * 分页查询任务监控配置
     * @Author: zy
     * @Date: 2025/7/25 11:08
     * @param queryFM 查询参数
     * @return: java.util.List<com.dh.message.bean.vo.TaskMonitorSettingVO>
     **/
    List<TaskMonitorSettingVO> getTaskMonitorSettingList(TaskMonitorSettingQueryFM queryFM);

    /**
     * 获取所有的配置
     * @Author: zgj
     * @Date: 2025/7/29 上午9:52
     * @param id id
     * @return: com.dh.message.bean.entity.TaskMonitorSetting
     **/
    TaskMonitorSetting getAllById(Long id);

    /**
     * 根据业务类型查询任务监控配置详情
     * @Author: zy
     * @Date: 2025/7/25 11:08
     * @param businessType businessType 业务类型
     * @return: com.dh.message.bean.vo.TaskMonitorSettingCardVO 任务监控配置VO
     **/
    TaskMonitorSettingCardVO getTaskMonitorSettingByBusinessType(String businessType);
}
