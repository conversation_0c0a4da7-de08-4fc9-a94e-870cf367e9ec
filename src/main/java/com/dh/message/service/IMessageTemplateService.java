package com.dh.message.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dh.message.entity.MessageTemplate;

/**
 * <p>
 * 消息模板 服务类
 * </p>
 *
 * <AUTHOR> @since 2021-01-26
 */
public interface IMessageTemplateService extends IService<MessageTemplate> {

    /**
     * 通过参数集合和模板id，返回模板内容
     *
     * @param id
     * @param paramsJson
     * @return
     */
    String getSubjectByTemplate(Long id, String paramsJson);

    /**
     * 根据模板编码，查询模板信息
     * @param code
     * @return
     */
    MessageTemplate getByTemplateCode(String code);
}
