package com.dh.message.service;

import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * InMemoryLockManager 类用于管理内存中的锁，使用 ConcurrentHashMap 存储锁，
 * 支持尝试获取锁、释放锁和检查锁状态的操作。
 */
@Component
public class InMemoryLockService {

    private final ConcurrentHashMap<String, ReentrantLock> locks = new ConcurrentHashMap<>();

    /**
     * 尝试在指定的超时时间内获取指定锁键对应的锁。
     *
     * @param lockKey 要获取的锁的键
     * @param timeout 超时时间，单位为毫秒
     * @return 如果在超时时间内成功获取到锁，则返回 true；否则返回 false
     * @throws InterruptedException 如果当前线程在等待获取锁的过程中被中断
     */
    public boolean tryLock(String lockKey, long timeout) throws InterruptedException {
        ReentrantLock lock = locks.computeIfAbsent(lockKey, k -> new ReentrantLock());
        return lock.tryLock(timeout, TimeUnit.MILLISECONDS);
    }

    /**
     * 释放指定锁键对应的锁。
     * 如果锁存在且当前线程持有该锁，则释放该锁；
     * 如果释放后锁不再被锁定，则从锁映射中移除该锁。
     *
     * @param lockKey 要释放的锁的键
     */
    public void unlock(String lockKey) {
        ReentrantLock lock = locks.get(lockKey);
        if (lock != null && lock.isHeldByCurrentThread()) {
            lock.unlock();
            if (!lock.isLocked()) {
                locks.remove(lockKey);
            }
        }
    }

    /**
     * 检查指定的锁键对应的锁是否处于锁定状态。
     *
     * @param lockKey 要检查的锁的键
     * @return 如果锁存在且处于锁定状态，则返回 true；否则返回 false
     */
    public boolean isLocked(String lockKey) {
        ReentrantLock lock = locks.get(lockKey);
        return lock != null && lock.isLocked();
    }
}