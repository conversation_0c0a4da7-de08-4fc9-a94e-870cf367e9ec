package com.dh.message.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dh.dto.bean.dto.message.GetLastRunningTaskMonitorDTO;
import com.dh.dto.bean.dto.message.SubmitTaskMonitorDTO;
import com.dh.dto.bean.dto.message.SubmitTaskMonitorResDTO;
import com.dh.dto.bean.dto.message.UpdateTaskMonitorDTO;
import com.dh.message.bean.entity.TaskMonitor;
import com.dh.message.bean.fm.TaskMonitorFM;
import com.dh.message.bean.vo.TaskMonitorVO;
import com.dh.message.dto.TaskMonitorInfoDTO;

import java.util.List;

/**
 * <p>
 * 任务监控表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface TaskMonitorService extends IService<TaskMonitor> {
    /**
     * 分页获得进行中的任务
     * @param pageSize 页码大小
     * @param pageNum 页码
     * @return 进行中的任务
     */
    List<TaskMonitorInfoDTO> getInProgressTask(int pageSize, int pageNum);

    /**
     * 获得进行中任务数量
     * @return 进行中的任务数量
     */
    long getInProgressTaskCount();

    /**
     * 任务监控检测
     * @param taskMonitorInfoDTO 任务监控信息
     */
    void checkTask(TaskMonitorInfoDTO taskMonitorInfoDTO);

    /**
     * 发起任务监控
     * @Author: zgj
     * @Date: 2023-06-08 20:45
     * @param submitTaskMonitorDTO 提交任务监控信息
     * @return: java.lang.Long
     **/
    SubmitTaskMonitorResDTO submitTaskMonitor(SubmitTaskMonitorDTO submitTaskMonitorDTO);

    /**
     * 更新任务监控进度
     * @Author: zgj
     * @Date: 2025/7/25 下午6:39
     * @param updateTaskMonitorDTO 更新任务监控进度信息
     * @return: java.lang.Long
     **/
    Long updateTaskMonitorProgress(UpdateTaskMonitorDTO updateTaskMonitorDTO);

    /**
     * 根据条件查询任务列表
     * @Author: zy
     * @Date: 2025/7/28 9:51
     * @param fm 查询参数
     * @return: java.util.List<com.dh.message.bean.vo.TaskMonitorVO>
     **/
    List<TaskMonitorVO> getTaskMonitorList(TaskMonitorFM fm);

    /**
     * 获取最近一次进行中的任务监控数据
     * @Author: zgj
     * @Date: 2023-06-08 20:45
     * @param dto 入参
     * @return: com.dh.common.util.R<com.dh.dto.bean.dto.message.SubmitTaskMonitorResDTO>
     **/
    SubmitTaskMonitorResDTO getLastRunningTaskMonitor(GetLastRunningTaskMonitorDTO dto);
}
