package com.dh.message.service;

import com.dh.message.bean.entity.TaskMonitorAlert;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dh.message.dto.TriggerAlertDTO;

import java.util.List;

/**
 * <p>
 * 任务监控报警表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface TaskMonitorAlertService extends IService<TaskMonitorAlert> {
    /**
     * 添加报警记录
     *
     * @param triggerAlertDTO 报警信息
     * @param ccUsers         cc的用户
     */
    TaskMonitorAlert add(TriggerAlertDTO triggerAlertDTO, List<Long> ccUsers);
}
