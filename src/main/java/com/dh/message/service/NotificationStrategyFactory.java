package com.dh.message.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 通知策略工厂
 * 用于管理和获取不同的通知策略
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Component
public class NotificationStrategyFactory {
    @Autowired
    private List<NotificationStrategy> strategies;

    private final Map<String, NotificationStrategy> strategyMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        for (NotificationStrategy strategy : strategies) {
            strategyMap.put(strategy.getType().toLowerCase(), strategy);
        }
    }

    /**
     * 获取通知策略
     *
     * @param type 策略类型
     * @return 通知策略
     */
    public NotificationStrategy getStrategy(String type) {
        return strategyMap.get(type.toLowerCase());
    }

    /**
     * 检查是否支持该通知类型
     *
     * @param type 通知类型
     * @return 是否支持
     */
    public boolean supports(String type) {
        return strategyMap.containsKey(type.toLowerCase());
    }

    /**
     * 获取所有支持的通知类型
     *
     * @return 支持的通知类型列表
     */
    public List<String> getSupportedTypes() {
        return new ArrayList<>(strategyMap.keySet());
    }
} 