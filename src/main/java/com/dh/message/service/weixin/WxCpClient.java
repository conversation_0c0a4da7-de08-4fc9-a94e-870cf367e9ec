//package com.dh.message.service.weixin;
//
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import me.chanjar.weixin.common.error.WxErrorException;
//import me.chanjar.weixin.cp.api.WxCpService;
//import me.chanjar.weixin.cp.bean.message.WxCpMessage;
//import org.springframework.stereotype.Component;
//
///**
// * 微信企业号客户端
// */
//@Slf4j
//@AllArgsConstructor
//@Component
//public class WxCpClient {
//    private final WxCpService wxCpService;
//
//    /**
//     * 指定接收消息的成员，成员ID列表（多个接收者用‘|’分隔，最多支持1000个）。
//     * 特殊情况：指定为"@all"，则向该企业应用的全部成员发送
//     * @param wxUserNames
//     * @param message
//     * @return
//     */
//    private WxCpMessage build(String wxUserNames, String message) {
//
//        return WxCpMessage.TEXT()
//                .agentId(wxCpService.getWxCpConfigStorage().getAgentId())
//                .toUser(wxUserNames).content(message)
//                .build();
//    }
//
//    /**
//     * 发送企业微信消息
//     * @param wxUserNames 微信企业用户名
//     * @param message 微信消息
//     */
//    public void pushToUser(String wxUserNames, String message) {
//        try {
//            wxCpService.getMessageService().send(build(wxUserNames, message));
//        } catch (WxErrorException e) {
//            log.error(String.format("发送企业微信失败，微信企业用户名:%s", wxUserNames), e);
//
//            throw new WxCpPushToUserException(wxUserNames, e);
//        }
//    }
//}
