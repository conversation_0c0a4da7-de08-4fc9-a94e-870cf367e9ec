package com.dh.message.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dh.message.entity.MessagePush;

import java.util.List;

public interface MessagePushService {

    MessagePush getMessagePush(String messagePushId);

    int insertMessagePush(MessagePush messagePush);

    /**
     * 根据条件查询
     *
     * @param queryWrapper
     * @return
     */
    List<MessagePush> getMessagePushList(QueryWrapper<MessagePush> queryWrapper);

    /**
     * 更新推送消息 (多个)
     *
     * @param messagePushList
     * @return
     */
    void updateMessagePushList(List<MessagePush> messagePushList);

    /**
     * 更新推送消息(单个)
     *
     * @param messagePush
     * @return
     */
    boolean updateMessagePush(MessagePush messagePush);

    /**
     * 删除推送消息
     *
     * @param idList
     * @return
     */
    boolean deleteMessagePush(List<String> idList);
}
