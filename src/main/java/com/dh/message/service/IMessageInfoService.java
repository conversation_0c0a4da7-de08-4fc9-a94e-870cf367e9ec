package com.dh.message.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dh.message.controller.vo.DeleteMessageCmd;
import com.dh.message.controller.vo.HandleMessageCmd;
import com.dh.message.dto.MessageInfoDTO;
import com.dh.message.entity.MessageInfo;

import java.util.List;

/**
 * <p>
 * 消息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-09
 */
public interface IMessageInfoService extends IService<MessageInfo> {

    void saveMessageInfo(MessageInfoDTO messageInfo);

    IPage<MessageInfo> findMessageByCondition(Page page, Integer messageType, String subject);

    List<MessageInfo> getByBusinessId(String businessId, String receiveTo);

    List<MessageInfo> getByBusinessIdAndType(String businessId, String businessType, Long receiveTo);

    MessageInfo findMessageExist(String businessId, String businessType, int msgType, Long receiveTo);

    void read(Long id);

    void handle(HandleMessageCmd cmd);

    void delete(DeleteMessageCmd cmd);
}
