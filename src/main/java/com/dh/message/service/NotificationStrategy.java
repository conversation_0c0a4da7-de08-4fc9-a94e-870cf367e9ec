package com.dh.message.service;

import com.dh.message.bean.bo.NotificationConfig;
import com.dh.message.bean.bo.NotifyTarget;
import com.dh.message.bean.bo.NotifyTo;
import com.dh.message.bean.bo.RelatedNotificationConfig;
import com.dh.message.dto.TaskMonitorInfoDTO;

import java.util.concurrent.Future;

/**
 * 通知策略接口
 * 用于实现不同通知类型的发送策略
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
public interface NotificationStrategy {
    /**
     * 获取策略类型
     */
    String getType();

    /**
     * 发送通知
     *
     * @param template           通知模板
     * @param notifyTo           通知目标
     * @param taskMonitorInfoDTO TaskMonitorInfoDTO
     * @return 是否发送成功
     */
    Future<Boolean> send(String template, NotifyTo notifyTo, TaskMonitorInfoDTO taskMonitorInfoDTO,
                         NotificationConfig notificationConfig);

    /**
     * 发送责任人通知
     *
     * @param template           通知模板
     * @param userId         用户Id
     * @param taskMonitorInfoDTO TaskMonitorInfoDTO
     * @return 是否发送成功
     */
    Future<Boolean> send(String template, Long userId, TaskMonitorInfoDTO taskMonitorInfoDTO,
                         RelatedNotificationConfig.RemindConfig relatedNotificationConfig);

    /**
     * 验证通知参数
     *
     * @param notifyTarget 通知目标
     */
    boolean validate(NotifyTarget notifyTarget);
} 