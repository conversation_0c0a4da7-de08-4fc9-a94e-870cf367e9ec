package com.dh.message.service;

import com.dh.message.entity.MessagePushHis;

import java.util.List;

public interface MessagePushHisService {

     MessagePushHis getMessagePushHis(String messagePushId);

    /**
     * 保存推送消息归档（多个）
     *
     * @param messagePushHisList
     * @return
     */
     void saveMessagePushHisList(List<MessagePushHis> messagePushHisList);

    /**
     * 保存推送消息归档（单个）
     *
     * @param messagePushHis
     * @return
     */
     boolean saveMessagePushHis(MessagePushHis messagePushHis);


}
