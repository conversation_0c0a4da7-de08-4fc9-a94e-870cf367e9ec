//package com.dh.message.service.email;
//
//import freemarker.template.Configuration;
//import freemarker.template.Template;
//import freemarker.template.TemplateException;
//import lombok.AllArgsConstructor;
//import org.springframework.boot.autoconfigure.mail.MailProperties;
//import org.springframework.core.io.Resource;
//import org.springframework.mail.javamail.JavaMailSender;
//import org.springframework.mail.javamail.MimeMessageHelper;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//
//import javax.mail.MessagingException;
//import javax.mail.internet.MimeMessage;
//import java.io.IOException;
//import java.io.StringWriter;
//import java.util.Collections;
//import java.util.Map;
//
///**
// * 邮件客户端
// */
//@AllArgsConstructor
//@Component
//public class EmailClient {
//
//    private final JavaMailSender sender;
//
//    private final Configuration freeMarkerConfiguration;
//
//    private final MailProperties mailProperties;
//
//    public void send(String subject, String templateName, Object dataModel, String...to) throws MessagingException, TemplateException, IOException {
//        send(subject, templateName, dataModel, Collections.emptyMap(), Collections.emptyMap(), to);
//    }
//
//    public void send(String subject, String templateName, Object dataModel, String[] to, String ...cc) throws MessagingException, TemplateException, IOException {
//        send(subject, templateName, dataModel, Collections.emptyMap(), Collections.emptyMap(), to, cc);
//    }
//
//    public void send(String subject, String templateName, Object dataModel, Map<String, Resource> attachments, String[] to, String ...cc) throws MessagingException, TemplateException, IOException {
//        send(subject, templateName, dataModel, attachments, Collections.emptyMap(), to, cc);
//    }
//
//    public void send(String subject, String templateName, Object dataModel, Map<String, Resource> attachments, Map<String, Resource> inlines, String[] to, String ...cc) throws MessagingException, TemplateException, IOException {
//        Template emailTemplate = freeMarkerConfiguration.getTemplate(templateName);
//        StringWriter sw = new StringWriter();
//        emailTemplate.process(dataModel, sw);
//
//        send(subject, sw.toString(), attachments, inlines, true, to, cc);
//    }
//
//    public void send(String subject, String content, boolean html, String[] to, String ...cc) throws MessagingException {
//        send(subject, content, Collections.emptyMap(), Collections.emptyMap(), html, to, cc);
//    }
//
//    /**
//     * 发送邮件
//     * @param subject 主题
//     * @param content 内容
//     * @param attachments 附件
//     * @param inlines 内联资源, 通过Content-ID关联资源, 注意先天设置邮件内容，在添加内联资源，否则可能无效果
//     *        eg: inlines------> Map->key: identifier1234   Map->resource: new FileSystemResource("c:/Sample.jpg")
//     *            content------> <html><body><img src='cid:identifier1234'></body></html>
//     *        邮件内容Html中的img对象(通过identifier1234标识）将被替换成Map参数中Key为identifier1234图片
//     * @param html 是否Html
//     * @param to 邮件接收人
//     */
//    public void send(String subject, String content, Map<String, Resource> attachments, Map<String, Resource> inlines, boolean html, String[] to, String ...cc) throws MessagingException {
//        MimeMessage message = sender.createMimeMessage();
//        MimeMessageHelper helper = new MimeMessageHelper(message, true);
//
//        helper.setFrom(mailProperties.getUsername());
//        helper.setTo(to);
//        if(cc != null && cc.length > 0) {
//            helper.setCc(cc);
//        }
//        helper.setSubject(subject);
//        helper.setText(content, html);
//        if(!CollectionUtils.isEmpty(attachments)) {
//            for (Map.Entry<String, Resource> attEntry : attachments.entrySet()) {
//                helper.addAttachment(attEntry.getKey(), attEntry.getValue());
//            }
//        }
//        if(!CollectionUtils.isEmpty(inlines)) {
//            for (Map.Entry<String, Resource> inlineEntry : inlines.entrySet()) {
//                helper.addInline(inlineEntry.getKey(), inlineEntry.getValue());
//            }
//        }
//        sender.send(message);
//    }
//}
