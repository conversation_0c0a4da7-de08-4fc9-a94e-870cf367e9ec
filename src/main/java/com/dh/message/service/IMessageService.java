package com.dh.message.service;

import com.dh.common.util.R;
import com.dh.message.dto.SendMessageDTO;

import java.util.List;

public interface IMessageService {

    /**
     * 发送消息
     * @param sendMessage
     * @return
     */
    R sendMessage(SendMessageDTO sendMessage);

    /**
     * 设置消息读取状态
     * @param messageIds 消息ID集合
     * @param readFlag 是否已读（0：否，1：是）
     */
    void setRead(List<Long> messageIds, int readFlag);

}
