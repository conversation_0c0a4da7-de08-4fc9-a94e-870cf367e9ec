package com.dh.message.constant;

import com.dh.common.model.KeyValue;

public final class DbConstant {

    private DbConstant() {

    }
    /**
     * 全局最大数据查询数量
     */
    public static final int GLOBAL_DATA_MAX_LIMIT = 10000;

    /**
     * 通用数据库字段：创建时间
     */
    public static final KeyValue<String, String> FIELD_CREATE_DATE = new KeyValue<>("create_date", "createDate");
    /**
     * 通用数据库字段：创建人
     */
    public static final KeyValue<String, String> FIELD_CREATE_BY = new KeyValue<>("create_by", "createBy");
    /**
     * 通用数据库字段：更新时间
     */
    public static final KeyValue<String, String> FIELD_UPDATE_DATE = new KeyValue<>("update_date", "updateDate");
    /**
     * 通用数据库字段：更新人
     */
    public static final KeyValue<String, String> FIELD_UPDATE_BY = new KeyValue<>("update_by", "updateBy");

    /**
     * 通用数据库字段：逻辑删除字段
     */
    public static final KeyValue<String, String> FIELD_LOGIC_DELETE = new KeyValue<>("del_flag", "delFlag");
}
