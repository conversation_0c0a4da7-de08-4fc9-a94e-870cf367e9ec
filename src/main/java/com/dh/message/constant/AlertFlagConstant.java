package com.dh.message.constant;

import lombok.extern.slf4j.Slf4j;

/**
 * 报警标志定义
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
public class AlertFlagConstant {
    public final static int MAX_LEVEL = 3;
    /**
     * 三级预警 1(0x00000001)
     */
    public final static int LEVEL3_WARNING_FLAG = 1;
    /**
     * 三级报警 2(0x00000010)
     */
    public final static int LEVEL3_ALERT_FLAG = 1 << 1;
    /**
     * 二级预警 4(0x00000100)
     */
    public final static int LEVEL2_WARNING_FLAG = 1 << 2;
    /**
     * 二级报警 8(ox00001000)
     */
    public final static int LEVEL2_ALERT_FLAG = 1 << 3;
    /**
     * 一级预警 16(0x00010000)
     */
    public final static int LEVEL1_WARNING_FLAG = 1 << 4;
    /**
     * 一级报警 32(0x00100000)
     */
    public final static int LEVEL1_ALERT_FLAG = 1 << 5;

    /**
     * 获得报警级别标志位
     *
     * @param level     级别
     * @param isWarning 是否预警
     * @return 报警级别标志位
     */
    public static int getLevelFlag(int level, boolean isWarning) {
        return 1 << ((MAX_LEVEL - level) * 2 + (isWarning ? 0 : 1));
    }

    public static int calcLevel(int levelFlag, int maxLevel) {
        for (int level = maxLevel; level > 0; level--) {
            int flag = levelFlag & getLevelFlag(level, false);
            if(flag != 0) {
                return level;
            }
        }

        return 0;
    }

    public static int calcLevel(int levelFlag) {
        return calcLevel(levelFlag, MAX_LEVEL);
    }

    public static void main(String[] args) {
        int alertFlag = 7;
        log.info(">> {}", calcLevel(8));
        //alertFlag |= getLevelFlag(1, false);
        //log.debug("alertFlag: {}", alertFlag);
        //log.debug("2 => {}", LEVEL2_WARNING_FLAG | LEVEL2_ALERT_FLAG, level2);
        //int level3 = LEVEL3_WARNING_FLAG | LEVEL3_ALERT_FLAG;
        //log.debug("3 => {}", LEVEL3_WARNING_FLAG | LEVEL3_ALERT_FLAG);
        //int level2 = level3 | LEVEL2_WARNING_FLAG | LEVEL2_ALERT_FLAG;
        //log.debug("2 => {} - {}", LEVEL2_WARNING_FLAG | LEVEL2_ALERT_FLAG, level2);
        //int level1 = level2 | LEVEL1_WARNING_FLAG | LEVEL1_ALERT_FLAG;
        //log.debug("1 => {} - {}", LEVEL1_WARNING_FLAG | LEVEL1_ALERT_FLAG, level1);
        //for (int i = MAX_LEVEL; i > 0; i--) {
        //    log.info("{} - {}", i, getLevelFlag(i, true));
        //    log.info("{} - {}", i, getLevelFlag(i, false));
        //}
        //int alertFlag =  LEVEL3_WARNING_FLAG | LEVEL3_ALERT_FLAG;
        log.info("LEVEL3_WARNING_FLAG: {}", ((alertFlag & LEVEL3_WARNING_FLAG) != 0));
        log.info("LEVEL3_ALERT_FLAG: {}", ((alertFlag & LEVEL3_ALERT_FLAG) != 0));
        log.info("LEVEL2_WARNING_FLAG: {}", ((alertFlag & LEVEL2_WARNING_FLAG) != 0));
        log.info("LEVEL2_ALERT_FLAG: {}", ((alertFlag & LEVEL2_ALERT_FLAG) != 0));
        log.info("LEVEL1_WARNING_FLAG: {}", ((alertFlag & LEVEL1_WARNING_FLAG) != 0));
        log.info("LEVEL1_WARNING_FLAG: {}", ((alertFlag & LEVEL1_ALERT_FLAG) != 0));
    }
}