package com.dh.message;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.po.TableFill;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;
import com.dh.common.constant.DbConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.env.YamlPropertySourceLoader;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.util.*;

/**
 * 〈一句话功能简述〉<br>
 * 〈〉
 *
 * <AUTHOR>
 */
@Slf4j
public final class CodeGenerator {

    private CodeGenerator() {

    }

    /**
     * <p>
     * 读取控制台内容
     * </p>
     */
    private static String scanner(String tip) {
        Scanner scanner = new Scanner(System.in, "UTF-8");
        StringBuilder help = new StringBuilder();
        help.append("请输入" + tip + "：");
        log.info(help.toString());
        if (scanner.hasNext()) {
            String ipt = scanner.next();
            if (StringUtils.isNotEmpty(ipt)) {
                return ipt;
            }
        }
        throw new MybatisPlusException("请输入正确的" + tip + "！");
    }

    public static void main(String[] args) {
        boolean override = scanner("覆盖已有文件Y/N?").equalsIgnoreCase("Y");
        String tableNames = scanner("表名");
        if (!StringUtils.isEmpty(tableNames)) {
            Arrays.stream(tableNames.split(",")).forEach(tb -> {
                if (!StringUtils.isEmpty(tb)) {
                    log.info("================准备生成为表【" + tb + "】生成文件！=====================");
                    generate(override, tb);
                }
            });
        }
    }

    private static void generate(boolean override, String tableName) {
        YamlPropertySourceLoader resourceLoader = new YamlPropertySourceLoader();
        List<PropertySource<?>> ps;
        String projectName;
        try {
            ps = resourceLoader.load("application",
                    new ClassPathResource("application" + ".yml"));
            PropertySource<?> properties = ps.get(0);
            projectName = Objects.requireNonNull(properties.getProperty("spring.application.name")).toString();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        // 代码生成器
        AutoGenerator mpg = new AutoGenerator();
        log.info(System.getProperties().toString());
        // 全局配置
        GlobalConfig gc = new GlobalConfig();
        String projectCopyPath = System.getProperty("user.dir");
        String[] split = projectCopyPath.split("\\\\");
        if (!("dh-" + projectName).equals(split[split.length - 1])) {
            projectCopyPath = projectCopyPath + "/dh-" + projectName;
        }
        final String projectPath = projectCopyPath;
        //生成文件的输出目录
        gc.setOutputDir(projectPath + "/src/main/java");
        gc.setAuthor("author");
        gc.setOpen(false);
        gc.setFileOverride(override);
        //gc.setActiveRecord(true);
        //开启 baseColumnList 生成通用sql字段
        gc.setBaseColumnList(true);
        //开启 BaseResultMap
        gc.setBaseResultMap(true);
        //指定生成的主键的ID类型
        gc.setIdType(IdType.ID_WORKER);
        //时间类型对应策略
        gc.setDateType(DateType.TIME_PACK);
        gc.setServiceName("%sService");

        // 数据源配置
        dataSourceConfig(mpg);

        // 包配置
        PackageConfig pc = new PackageConfig();
        pc.setParent("com.dh." + projectName);
        //mapper包名
        pc.setMapper("mapper");
        pc.setEntity("bean.entity");
        mpg.setPackageInfo(pc);

        // 自定义配置
        InjectionConfig cfg = new InjectionConfig() {
            @Override
            public void initMap() {
                // to do nothing
            }
        };
        List<FileOutConfig> focList = new ArrayList<>();
        focList.add(new FileOutConfig("/templates/mapper.xml.vm") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                return projectPath + "/src/main/resources/mapper/"
                        + tableInfo.getEntityName() + "Mapper" + StringPool.DOT_XML;
            }
        });

        // 实体类文件输出
        focList.add(new FileOutConfig("/templates/entity.java.vm") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                return projectPath + "/src/main/java/com/dh/" + projectName + "/bean/entity/" + tableInfo.getEntityName() + StringPool.DOT_JAVA;
            }
        });

        cfg.setFileOutConfigList(focList);
        mpg.setCfg(cfg);
        mpg.setTemplate(new TemplateConfig().setXml(null).setEntity(null));

        // 策略配置
        StrategyConfig strategy = new StrategyConfig();
        strategy.setNaming(NamingStrategy.underline_to_camel); //数据库表映射到实体的，命名策略
        strategy.setColumnNaming(NamingStrategy.underline_to_camel);
        strategy.setEntityLombokModel(true);
        //strategy.setSuperControllerClass("com.nz.xplat.common.BaseController");
        strategy.setInclude(tableName);
        strategy.setRestControllerStyle(true);
        //驼峰转连字符
        strategy.setControllerMappingHyphenStyle(false);
        strategy.setTablePrefix("dc_");
        //设置自动填充字段
        List<TableFill> fieldList = new ArrayList<>();
        fieldList.add(new TableFill(DbConstant.FIELD_CREATE_BY.getKey(), FieldFill.INSERT));
        fieldList.add(new TableFill(DbConstant.FIELD_CREATE_DATE.getKey(), FieldFill.INSERT));
        fieldList.add(new TableFill(DbConstant.FIELD_UPDATE_BY.getKey(), FieldFill.INSERT_UPDATE));
        fieldList.add(new TableFill(DbConstant.FIELD_UPDATE_DATE.getKey(), FieldFill.INSERT_UPDATE));
        strategy.setTableFillList(fieldList);
        //设置逻辑删除字段
        strategy.setLogicDeleteFieldName(DbConstant.FIELD_LOGIC_DELETE.getKey());
        //是否生成 serialVersionUID
        strategy.setEntitySerialVersionUID(true);
        //是否生成实体时，生成@TableField内容
        strategy.setEntityTableFieldAnnotationEnable(true);

        mpg.setStrategy(strategy);
        // 选择 freemarker 引擎需要指定如下加，注意 pom 依赖必须有！
        // FreemarkerTemplateEngine识别.ftl模板文件， VelocityTemplateEngine识别.vm模板文件
        mpg.setTemplateEngine(new VelocityTemplateEngine());
        //开启 swagger2 模式
        gc.setSwagger2(true);
        mpg.setGlobalConfig(gc);
        mpg.execute();
    }

    /**
     * 数据源配置
     *
     * @param generator
     */
    private static void dataSourceConfig(AutoGenerator generator) {
        DataSourceConfig dsc = new DataSourceConfig();
        try {
            //InputStream in = ClassLoader.getSystemResourceAsStream("generator.properties");
            //Properties properties = new Properties();
            //properties.load(in);
            YamlPropertySourceLoader resourceLoader = new YamlPropertySourceLoader();
            List<PropertySource<?>> ps = resourceLoader.load("application",
                    new ClassPathResource("application-dev" + ".yml"));
            PropertySource<?> properties = ps.get(0);

            // dsc.setSchemaName("public");
            dsc.setUrl(Objects.requireNonNull(properties.getProperty("spring.datasource.url")).toString());
            dsc.setDriverName(Objects.requireNonNull(
                    properties.getProperty("spring.datasource.driver-class-name")).toString());
            dsc.setUsername(Objects.requireNonNull(
                    properties.getProperty("spring.datasource.username")).toString());
            dsc.setPassword(Objects.requireNonNull(
                    properties.getProperty("spring.datasource.password")).toString());
            generator.setDataSource(dsc);
        } catch (IOException e) {
            log.error("数据源配置异常：", e);
        }
    }
}
