package com.dh.message.util;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString
public class R<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private static final String OK = "OK";

    private static final String FAIL = "FAIL";

    private String code;

    private String msg;

    private T data;

    public static <T> R<T> ok() {
        return new R(OK, "", null);
    }

    public static <T> R<T> ok(T data) {
        return new R(OK, "", data);
    }

    public static <T> R<T> ok(String message, T data) {
        return new R(OK, message, data);
    }

    public static <T> R<T> fail() {
        return new R(FAIL, "", null);
    }

    public static <T> R<T> failed(String msg) {
        return new R(FAIL, msg, null);
    }

    public static <T> R<T> failed(String msg, T data) {
        return new R(FAIL, msg, data);
    }

    public static <T> R<T> failed(String code, String msg, T data) {
        return new R(code, msg, data);
    }
}

