package com.dh.message.util;

import com.dh.message.entity.MessagePush;
import com.dh.message.entity.MessagePushHis;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public final class EntityConvert {

    private EntityConvert() {

    }

    /**
     * messagePush转换为messagePushHis
     *
     * @param list
     * @return
     */
    public static List<MessagePushHis> messagePushConvertMessagePushHis(List<MessagePush> list) {
        List<MessagePushHis> returnDataList = new ArrayList<>();
        for (MessagePush messagePush : list) {
            MessagePushHis messagePushHis = new MessagePushHis();
            messagePushHis.setId(messagePush.getId());
            messagePushHis.setMsgType(messagePush.getMsgType()); //消息类型
            messagePushHis.setSendChannel(messagePush.getSendChannel()); //推送渠道
            messagePushHis.setSubject(messagePush.getSubject()); //消息主题
            messagePushHis.setContent(messagePush.getContent()); //消息内容
            messagePushHis.setMsgUrl(messagePush.getMsgUrl()); //消息跳转链接
            messagePushHis.setReceiveTo(messagePush.getReceiveTo()); //消息接收人
            messagePushHis.setReceiveToNum(messagePush.getReceiveToNum()); //消息接收人 号码
            messagePushHis.setCreateBy(messagePush.getCreateBy()); // 创建人
            messagePushHis.setCreateTime(messagePush.getCreateTime()); // 创建时间
            messagePushHis.setStatus(messagePush.getStatus()); // 0 无效  1有效
            messagePushHis.setSendStatus(messagePush.getSendStatus()); // 0 未推送 1推送成功 2 推送失败
            messagePushHis.setSendTime(messagePush.getSendTime()); // 推送时间
            messagePushHis.setSendFailTimes(messagePush.getSendFailTimes()); // 推送失败次数
            messagePushHis.setSendFailMsg(messagePush.getSendFailMsg()); // 推送失败原因
            messagePushHis.setMsgArchiveTime(LocalDateTime.now()); //归档时间

            returnDataList.add(messagePushHis);
        }
        return returnDataList;
    }
}
