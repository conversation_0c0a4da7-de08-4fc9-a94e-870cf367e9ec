package com.dh.message.util;

import org.springframework.util.PropertyPlaceholderHelper;

import java.util.Map;

/**
 * 模板渲染工具类
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
public class TemplateRenderUtil {
    public static String render(String template, Map<String, String> params) {
        if (template == null || template.isEmpty()) {
            return null;
        }
        if (params == null || params.isEmpty()) {
            return template;
        }

        PropertyPlaceholderHelper helper = new PropertyPlaceholderHelper("${", "}");
        return helper.replacePlaceholders(template, params::get);
    }
}
