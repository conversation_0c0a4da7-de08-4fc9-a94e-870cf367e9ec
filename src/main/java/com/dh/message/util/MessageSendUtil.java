package com.dh.message.util;

import com.dh.message.entity.MessagePush;
import lombok.extern.slf4j.Slf4j;

/**
 * 消息推送
 */
@Slf4j
public final class MessageSendUtil {

    private MessageSendUtil() {

    }
    /**
     * 短信渠道推送
     *
     * @param messagePush
     * @return
     */
    public static boolean sendByShortMsg(MessagePush messagePush) {
        //TODO
        long tt = System.currentTimeMillis();
        return tt % 2 == 0;
    }

    /**
     * 邮件渠道推送
     *
     * @param messagePush
     * @return
     */
    public static boolean sendByEmail(MessagePush messagePush) {
        //TODO
        long tt = System.currentTimeMillis();
        return tt % 2 == 0;
    }

    /**
     * APP渠道推送
     *
     * @param messagePush
     * @return
     */
    public static boolean sendByAppMsg(MessagePush messagePush) {
        //TODO
        long tt = System.currentTimeMillis();
        return tt % 2 == 0;
    }
}
