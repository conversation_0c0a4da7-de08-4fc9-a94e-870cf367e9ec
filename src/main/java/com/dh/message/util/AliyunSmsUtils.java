package com.dh.message.util;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.QuerySendDetailsResponse;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.dh.dto.util.SysPublicParamUtil;
import com.dh.framework.security.exception.BusinessException;
import com.dh.message.config.AliyunSmsConfig;
import com.dh.message.entity.MessageAliyunSmsRecord;
import com.dh.message.enums.SysPublicParamEnum;
import com.dh.message.service.IMessageAliyunSmsRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Slf4j
@Component
public final class AliyunSmsUtils {

    private AliyunSmsUtils() {

    }

    //产品域名
    protected static final String SYS_DOMAIN = "dysmsapi.aliyuncs.com";

    protected static final String REGION_ID = "cn-hangzhou";

    /**
     * 单条短信发送 适用于验证码等场景
     *
     * @param phoneNumbers 手机号码（多个使用逗号分隔）
     * @param params       内容参数  eg:{"code":"1111"}
     * @return
     * @throws Exception
     */
    public static SendSmsResponse sendSms(AliyunSmsConfig aliyunSmsConfig,
                                          IMessageAliyunSmsRecordService messageAliyunSmsRecordService,
                                          String phoneNumbers, String params, String templateCode) throws Exception {
        //可自助调整超时时间
        /*System.setProperty("sun.net.client.defaultConnectTimeout",
                String.valueOf(aliyunSmsConfig.getDefaultConnectTimeout()));
        System.setProperty("sun.net.client.defaultReadTimeout",
                String.valueOf(aliyunSmsConfig.getDefaultReadTimeout()));*/
        DefaultProfile profile = DefaultProfile.getProfile(REGION_ID,
                aliyunSmsConfig.getAccessKeyId(),
                aliyunSmsConfig.getAccessKeySecret());
        IAcsClient client = new DefaultAcsClient(profile);

        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain(SYS_DOMAIN);
        request.setSysVersion("2017-05-25");
        request.setSysAction("SendSms");
        request.putQueryParameter("RegionId", REGION_ID);
        //接收短信的手机号码。
        request.putQueryParameter("PhoneNumbers", phoneNumbers);
        //短信签名名称 请在控制台签名管理页面签名名称一列查看。
        String signName = SysPublicParamUtil.getStr(SysPublicParamEnum.SMS_SIGN_NAME.name());
        if (StringUtils.isEmpty(signName)) {
            // 如果没有签名，则提醒
            log.error("短信发送失败！短信签名未配置，请在参数管理中添加SMS_SIGN_NAME的参数配置！");
            throw new BusinessException("短信发送失败！短信签名未配置，请在参数管理中添加SMS_SIGN_NAME的参数配置！",
                    "The text message failed to send! The SMS signature is not configured. Please add the parameter configuration of SMS_SIGN_NAME in the parameter management!");
        }
        request.putQueryParameter("SignName", signName);
        //短信模板ID。请在控制台模板管理页面模板CODE一列查看
        request.putQueryParameter("TemplateCode", templateCode);
        //外部流水扩展字段。
        //        request.putQueryParameter("OutId", "cn-hangzhou");
        //上行短信扩展码，无特殊需要此字段的用户请忽略此字段。
        //        request.putQueryParameter("SmsUpExtendCode", "");
        //{"code":"1111"}
        request.putQueryParameter("TemplateParam", params);
        CommonResponse response = client.getCommonResponse(request);
        log.info(response.getData());
        SendSmsResponse sendSmsResponse = JSONObject.parseObject(response.getData(), SendSmsResponse.class);
        //{"Message":"OK","RequestId":"E1F436ED-4DF7-44FF-A7CC-1DED3D8FD3DA","BizId":"220010885988903665^0","Code":"OK"}

        MessageAliyunSmsRecord messageAliyunSmsRecord = new MessageAliyunSmsRecord();
        messageAliyunSmsRecord.setTemplateCode(templateCode);
        messageAliyunSmsRecord.setPhoneNumbers(phoneNumbers);
        messageAliyunSmsRecord.setReturnCode(sendSmsResponse.getCode());
        messageAliyunSmsRecord.setReturnMsg(sendSmsResponse.getMessage());
        messageAliyunSmsRecord.setReturnRequestid(sendSmsResponse.getRequestId());
        messageAliyunSmsRecord.setReturnBizid(sendSmsResponse.getBizId());
        messageAliyunSmsRecord.setParams(params);
        messageAliyunSmsRecord.setReturnStr(response.getData());
        messageAliyunSmsRecordService.save(messageAliyunSmsRecord);
        return sendSmsResponse;
    }

    /**
     * 短信发送查询
     *
     * @param bizId
     * @param phoneNumber
     * @return
     * @throws Exception
     */
    public static QuerySendDetailsResponse querySendDetails(AliyunSmsConfig aliyunSmsConfig,
                                                            String bizId,
                                                            String phoneNumber,
                                                            String sendDate) throws Exception {
        /*//可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout",
                String.valueOf(aliyunSmsConfig.getDefaultConnectTimeout()));
        System.setProperty("sun.net.client.defaultReadTimeout",
                String.valueOf(aliyunSmsConfig.getDefaultReadTimeout()));*/
        DefaultProfile profile = DefaultProfile
                .getProfile(REGION_ID, aliyunSmsConfig.getAccessKeyId(), aliyunSmsConfig.getAccessKeySecret());
        IAcsClient client = new DefaultAcsClient(profile);

        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain(SYS_DOMAIN);
        request.setSysVersion("2017-05-25");
        request.setSysAction("QuerySendDetails");
        request.putQueryParameter("RegionId", REGION_ID);

        //接收短信的手机号码。 格式： 国内短信：11位手机号码，例如15900000000。 国际/港澳台消息：国际区号+号码，例如85200000000。
        request.putQueryParameter("PhoneNumber", phoneNumber);
        //短信发送日期，支持查询最近30天的记录。 格式为yyyyMMdd，例如20181225。
        request.putQueryParameter("SendDate", sendDate);
        //分页查看发送记录，指定每页显示的短信记录数量。 取值范围为1~50。
        request.putQueryParameter("PageSize", "50");
        //分页查看发送记录，指定发送记录的的当前页码。
        request.putQueryParameter("CurrentPage", "1");
        //发送回执ID，即发送流水号。调用发送接口SendSms或SendBatchSms发送短信时，返回值中的BizId字段。
        request.putQueryParameter("BizId", bizId);

        CommonResponse response = client.getCommonResponse(request);
        log.info(response.getData());
        QuerySendDetailsResponse querySendDetailsResponse =
                JSONObject.parseObject(response.getData(), QuerySendDetailsResponse.class);
        return querySendDetailsResponse;
    }
}
