//package com.dh.message.util;
//
//import com.dh.common.dto.SysUserDTO;
//import org.springframework.context.i18n.LocaleContextHolder;
//import org.springframework.web.context.request.RequestContextHolder;
//import org.springframework.web.context.request.ServletRequestAttributes;
//public final class SecurityUtil {
//
//    private SecurityUtil() {
//
//    }
//
//    /**
//     * 当前登录用户
//     *
//     * @return
//     */
//    public static SysUserDTO getUser() {
//     /*   if (RequestContextHolder.getRequestAttributes() != null) {
//            HttpServletRequest request =
//                    ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
//            SysUserDTO user = (SysUserDTO) request.getAttribute("userInfo");
//            return user;
//        } else {
//            return null;
//        }*/
//
//        if (RequestContextHolder.getRequestAttributes() != null) {
//            ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
//                    .getRequestAttributes();
//            if (servletRequestAttributes != null) {
//                return  (SysUserDTO) servletRequestAttributes.getRequest().getAttribute("userInfo");
//            }
//        }
//        return null;
//    }
//
//
//    /**
//     * 当前登录用户id
//     *
//     * @return
//     */
//    public static Long getUserId() {
//        SysUserDTO userDTO = getUser();
//        return userDTO == null ? null : userDTO.getId();
//
//
//        /*if (RequestContextHolder.getRequestAttributes() != null) {
//            HttpServletRequest request =
//                    ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
//            SysUserDTO user = (SysUserDTO) request.getAttribute("userInfo");
//            if (user != null) {
//                return user.getId();
//            } else {
//                return null;
//            }
//        } else {
//            return null;
//        }*/
//    }
//
//    /**
//     * 当前登陆用户的岗位
//     *
//     * @return
//     */
//    public static Long getUserPostId() {
//        SysUserDTO userDTO = getUser();
//        return userDTO == null ? null : userDTO.getPostId();
//
//       /* if (RequestContextHolder.getRequestAttributes() != null) {
//            HttpServletRequest request =
//                    ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
//            SysUserDTO user = (SysUserDTO) request.getAttribute("userInfo");
//            if (user != null) {
//                return user.getPostId();
//            } else {
//                return null;
//            }
//        } else {
//            return null;
//        }*/
//    }
//
//    /**
//     * 当前登陆用户的姓名
//     *
//     * @return
//     */
//    public static String getUniqueName() {
//        SysUserDTO userDTO = getUser();
//        return userDTO == null ? null : userDTO.getRealName();
//
//       /* if (RequestContextHolder.getRequestAttributes() != null) {
//            HttpServletRequest request =
//                    ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
//            SysUserDTO user = (SysUserDTO) request.getAttribute("userInfo");
//            return user.getRealName();
//        } else {
//            return null;
//        }*/
//    }
//
//    public static String getLanguage() {
//        return LocaleContextHolder.getLocale().toLanguageTag();
//
//       /* return LocaleContextHolder.getLocale().toLanguageTag()
//                != null ? LocaleContextHolder.getLocale().toLanguageTag() : LanguageConstant.LANGUAGE_CN;*/
//    }
//}
