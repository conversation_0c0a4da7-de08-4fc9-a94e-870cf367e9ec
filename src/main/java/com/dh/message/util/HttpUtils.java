package com.dh.message.util;

import com.alibaba.fastjson.JSON;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.util.Map;

/**
 * @project: demo
 * @ClassName: HttpUtils
 * @author: Bin
 * @creat: 2021/6/3 10:48
 * 描述:
 */
public class HttpUtils {
    public static String httpPostMethod(String url, Map<String, Object> params) {
        String returnValue = "接口调用失败";
        CloseableHttpClient httpClient = HttpClients.createDefault();// 创建Httpclient对象
        HttpPost post = new HttpPost(url);// 创建Http Post请求
        post.setHeader("Content-Type","Content-Type");//设置请求头
        post.setEntity(new StringEntity(JSON.toJSONString(params), "utf-8"));//设置请求题
        CloseableHttpResponse response = null;
        try{
            response = httpClient.execute(post);// 执行http请求
            if(response != null && response.getStatusLine().getStatusCode() == 200){
                HttpEntity entity = response.getEntity();
                returnValue = EntityUtils.toString(entity);

            }
        }
        catch(Exception e)
        {
            e.printStackTrace();
        }

        finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                //
                e.printStackTrace();
            }
        }
        //第五步：处理返回值
        return returnValue;
    }

}
