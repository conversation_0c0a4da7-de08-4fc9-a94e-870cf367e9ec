//package com.dh.message.filter;
//
//import com.dh.common.dto.SysUserDTO;
//import com.dh.common.util.R;
//import com.dh.message.config.AuthProperties;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.core.ParameterizedTypeReference;
//import org.springframework.http.HttpMethod;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.util.AntPathMatcher;
//import org.springframework.util.CollectionUtils;
//import org.springframework.web.client.RestTemplate;
//
//import javax.servlet.Filter;
//import javax.servlet.FilterChain;
//import javax.servlet.FilterConfig;
//import javax.servlet.ServletException;
//import javax.servlet.ServletRequest;
//import javax.servlet.ServletResponse;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//
//@Slf4j
////@WebFilter(urlPatterns = "/*", filterName = "AuthUserFilter")
//@AllArgsConstructor
//public class AuthUserFilter implements Filter {
//
//    private static final AntPathMatcher ANTPATHMATCHER = new AntPathMatcher();
//
//    private AuthProperties authProperties;
//
//    private RestTemplate restTemplate;
//
//    private ObjectMapper objectMapper;
//
//    @Override
//    public void init(FilterConfig filterConfig) throws ServletException {
//        log.debug("AuthUserFilter init, loginUrl:{}, excludePaths:{}",
//                authProperties.getLoginUrl(),
//                authProperties.getExcludedPaths());
//    }
//
//    @Override
//    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
//            throws IOException, ServletException {
//        HttpServletRequest req = (HttpServletRequest) request;
//        HttpServletResponse res = (HttpServletResponse) response;
//
//        String servletPath = req.getServletPath();
//        if ("OPTIONS".equals(req.getMethod())) {
//            //不拦截跨域的预检请求，原因由于跨域的预检查请求中获取不到token信息，导致认证失败，最后导致跨域配置失效
//            chain.doFilter(request, response);
//            return;
//        }
//        //excluded path check
//        if (!CollectionUtils.isEmpty(authProperties.getExcludedPaths())) {
//            for (String excludedPath : authProperties.getExcludedPaths()) {
//                if (ANTPATHMATCHER.match(excludedPath, servletPath)) {
//                    chain.doFilter(request, response);
//                    return;
//                }
//            }
//        }
//        String token = req.getHeader("token");
//        long start = System.currentTimeMillis();
//        ResponseEntity<R<SysUserDTO>> responseEntity =
//                restTemplate.exchange(authProperties.getLoginUrl() + "?token=" + token, HttpMethod.GET,
//                null,
//                new ParameterizedTypeReference<R<SysUserDTO>>() {
//                });
//        log.debug(String.format("%s---方法执行需要%s毫秒",
//                authProperties.getLoginUrl() + "?token=" + token, System.currentTimeMillis() - start));
//        R<SysUserDTO> result = responseEntity.getBody();
//        if (result != null && "0".equals(result.getCode())) {
//            request.setAttribute("userInfo", result.getData());
//        } else {
//            res.setStatus(HttpStatus.UNAUTHORIZED.value());
//            res.setContentType("application/json;charset=UTF-8");
//            res.getWriter().println(objectMapper
//                    .writeValueAsString(R.failed(result == null ? "sso not login" : result.getMsg())));
//            return;
//        }
//
//        chain.doFilter(request, response);
//    }
//}
