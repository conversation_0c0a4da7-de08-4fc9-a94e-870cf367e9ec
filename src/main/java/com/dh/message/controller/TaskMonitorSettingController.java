package com.dh.message.controller;

import com.dh.common.util.R;
import com.dh.message.bean.fm.TaskMonitorSettingFM;
import com.dh.message.bean.fm.TaskMonitorSettingQueryFM;
import com.dh.message.bean.vo.TaskMonitorSettingCardVO;
import com.dh.message.bean.vo.TaskMonitorSettingVO;
import com.dh.message.service.TaskMonitorSettingService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 任务监控配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@RestController
@RequestMapping("/taskMonitorSetting")
@Api(tags = "任务监控配置管理")
@AllArgsConstructor
@Validated
public class TaskMonitorSettingController {

    private final TaskMonitorSettingService taskMonitorSettingService;


    @ApiOperation("分页查询任务监控配置")
    @PostMapping("/getTaskMonitorSettingList")
    public R<PageInfo<TaskMonitorSettingVO>> getTaskMonitorSettingList(@RequestBody TaskMonitorSettingQueryFM queryFM) {
        PageHelper.startPage(queryFM.getPageNum(), queryFM.getPageSize());
        List<TaskMonitorSettingVO> taskMonitorSettingList = taskMonitorSettingService.getTaskMonitorSettingList(queryFM);
        return R.ok(new PageInfo<>(taskMonitorSettingList));
    }

    @ApiOperation("查询任务监控配置详情")
    @GetMapping("/getTaskMonitorSettingDetail")
    public R<TaskMonitorSettingVO> getTaskMonitorSettingDetail(@RequestParam @NotNull(message = "ID不能为空") Long id) {
        TaskMonitorSettingVO vo = taskMonitorSettingService.getTaskMonitorSettingDetail(id);
        return R.ok(vo);
    }

    @ApiOperation("根据业务类型查询任务监控配置详情")
    @GetMapping("/getTaskMonitorSettingByBusinessType")
    public R<TaskMonitorSettingCardVO> getTaskMonitorSettingByBusinessType(@RequestParam  String BusinessType) {
        return R.ok(taskMonitorSettingService.getTaskMonitorSettingByBusinessType(BusinessType));
    }

    @ApiOperation("根据业务类型查询任务监控配置详情")
    @PostMapping("/getTaskMonitorSettingByBusinessTypeList")
    public R<List<TaskMonitorSettingCardVO>> getTaskMonitorSettingByBusinessTypeList(@RequestBody List<String> BusinessTypeList) {
        List<TaskMonitorSettingCardVO> resultList = new ArrayList<>();
        if(CollectionUtils.isEmpty(BusinessTypeList)) return R.ok(resultList);
        for (String BusinessType : BusinessTypeList) {
            TaskMonitorSettingCardVO vo = taskMonitorSettingService.getTaskMonitorSettingByBusinessType(BusinessType);
            if(vo != null) resultList.add(vo);
        }
        return R.ok(resultList);
    }

    @ApiOperation("保存或更新任务监控配置")
    @PostMapping("/saveOrUpdateTaskMonitorSetting")
    public R<Long> saveOrUpdateTaskMonitorSetting(@Validated @RequestBody TaskMonitorSettingFM fm) {
        return R.ok(taskMonitorSettingService.saveOrUpdateTaskMonitorSetting(fm));
    }

    @ApiOperation("删除任务监控配置")
    @PostMapping("/deleteTaskMonitorSetting")
    public R<String> deleteTaskMonitorSetting(@RequestParam @NotNull(message = "ID不能为空") Long id) {
        taskMonitorSettingService.removeById(id);
        return R.ok("删除成功");
    }


}

