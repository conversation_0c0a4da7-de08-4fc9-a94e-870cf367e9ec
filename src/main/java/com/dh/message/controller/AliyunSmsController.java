package com.dh.message.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.dysmsapi.model.v20170525.QuerySendDetailsResponse;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.dh.message.config.AliyunSmsConfig;
import com.dh.message.dto.SendAliyunSmsDto;
import com.dh.message.dto.SendAliyunSmsStatusDto;
import com.dh.message.entity.MessageAliyunSmsTemplate;
import com.dh.message.service.IMessageAliyunSmsRecordService;
import com.dh.message.task.LoadSystemInfoTask;
import com.dh.message.util.AliyunSmsUtils;
import com.dh.message.util.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * description
 *
 * <AUTHOR>
 * @since 2019/12/3
 */
@Slf4j
@RestController
@RequestMapping("/aliyunSms")
@Api(tags = "阿里云短信推送")
@AllArgsConstructor
public class AliyunSmsController {

    private AliyunSmsConfig aliyunSmsConfig;

    private IMessageAliyunSmsRecordService messageAliyunSmsRecordService;

    @ApiOperation(value = "推送短信验证码")
    @RequestMapping(value = "/sendSms", method = RequestMethod.POST)
    public R<SendSmsResponse> sendSms(@RequestBody SendAliyunSmsDto dto) {
        try {
            JSONObject jsonObj = (JSONObject) JSON.toJSON(dto.getParamsDto());
            log.info("[-----------]" + jsonObj.toJSONString());
            SendSmsResponse sendSmsResponse = AliyunSmsUtils.sendSms(aliyunSmsConfig,
                    messageAliyunSmsRecordService,
                    dto.getPhoneNumbers(), jsonObj.toJSONString(), dto.getTemplateCode());
            if (sendSmsResponse.getCode().equals("OK")) {
                return R.ok(sendSmsResponse).setMsg("success");
            } else {
                return R.failed("fail", sendSmsResponse);
            }
        } catch (Exception e) {
            log.error("sendSms exception.", e);
            return R.failed("exception");
        }
    }

    @ApiOperation(value = "推送短信")
    @RequestMapping(value = "/sendSmsNew/{templateCode}/{phoneNumber}", method = RequestMethod.POST)
    public R<SendSmsResponse> sendSmsNew(@PathVariable String templateCode, @PathVariable String phoneNumber,
                                         @RequestBody List<String> params) {
        try {
            MessageAliyunSmsTemplate template = LoadSystemInfoTask.getMap().get(templateCode);
            if (template == null) {
                return R.failed("template is not exists:" + templateCode);
            }
            String paramsJsonStr = template.getParamsJsonStr();
            log.info(paramsJsonStr);
            for (int i = 0; i < template.getParamsCount(); i++) {
                paramsJsonStr = paramsJsonStr.replace("PARAMS_" + i, params.get(i));
            }
            log.info(paramsJsonStr);
            SendSmsResponse sendSmsResponse = AliyunSmsUtils.sendSms(aliyunSmsConfig,
                    messageAliyunSmsRecordService,
                    phoneNumber, paramsJsonStr, templateCode);
            if (sendSmsResponse.getCode().equals("OK")) {
                return R.ok(sendSmsResponse).setMsg("success");
            } else {
                return R.failed("fail", sendSmsResponse);
            }
        } catch (Exception e) {
            log.error("sendSms exception.", e);
            return R.failed("exception");
        }
    }

    @ApiOperation(value = "推送短信SMS_190267841")
    @RequestMapping(value = "/sendSmsSMS190267841/{phoneNumber}/{examTime}", method = RequestMethod.POST)
    public R<SendSmsResponse> sendSmsSMS190267841(@PathVariable String phoneNumber,
                                                   @PathVariable String examTime) {
        try {
            String templateCode = "SMS_190267841";
            Map<String, String> paramsMap = new HashMap<>();
            paramsMap.put("ExamTime", examTime);
            JSONObject jsonObj = (JSONObject) JSON.toJSON(paramsMap);
            log.info("[-----------]" + jsonObj.toJSONString());
            SendSmsResponse sendSmsResponse = AliyunSmsUtils.sendSms(aliyunSmsConfig,
                    messageAliyunSmsRecordService,
                    phoneNumber, jsonObj.toJSONString(), templateCode);
            if (sendSmsResponse.getCode().equals("OK")) {
                return R.ok(sendSmsResponse).setMsg("success");
            } else {
                return R.failed("fail", sendSmsResponse);
            }
        } catch (Exception e) {
            log.error("sendSmsSMS190267841 exception.", e);
            return R.failed("exception");
        }
    }

    @ApiOperation(value = "查看消息推送状态")
    @RequestMapping(value = "/getSendDetails", method = RequestMethod.POST)
    public R<QuerySendDetailsResponse> getSendDetails(@RequestBody SendAliyunSmsStatusDto dto) {
        try {
            QuerySendDetailsResponse sendSmsResponse = AliyunSmsUtils.querySendDetails(aliyunSmsConfig,
                    dto.getBizId(), dto.getPhoneNumber(), dto.getSendDate());
            if (sendSmsResponse.getCode().equals("OK")) {
                return R.ok(sendSmsResponse).setMsg("success");
            } else {
                return R.failed("fail", sendSmsResponse);
            }
        } catch (Exception e) {
            log.error("getSendDetails exception.", e);
            return R.failed("exception");
        }
    }
}
