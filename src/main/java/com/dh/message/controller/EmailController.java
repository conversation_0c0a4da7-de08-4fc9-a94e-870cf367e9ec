//package com.dh.message.controller;
//
//import com.dh.common.util.R;
//import com.dh.message.controller.vo.SendEmailCmd;
//import com.dh.message.service.email.EmailClient;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.mail.MessagingException;
//
//@AllArgsConstructor
//@Slf4j
//@RestController
//@RequestMapping("/email")
//public class EmailController {
//
//    private final EmailClient emailClient;
//
//    @PostMapping("/send")
//    public R<String> send(@RequestBody SendEmailCmd sendEmailCmd) throws MessagingException {
//        emailClient.send(sendEmailCmd.getSubject(), sendEmailCmd.getContent(), sendEmailCmd.isHtml(), sendEmailCmd.getRec(), sendEmailCmd.getCc());
//
//        return R.ok();
//    }
//}
