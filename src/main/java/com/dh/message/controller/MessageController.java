package com.dh.message.controller;

import com.dh.common.util.R;
import com.dh.message.dto.SendMessageDTO;
import com.dh.message.service.IMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@RestController
@RequestMapping("/message")
@Api(tags = "消息管理")
@AllArgsConstructor
public class MessageController {

    private IMessageService messageService;

    @ApiOperation("发送消息")
    @PostMapping("/send")
    public R send(@Validated @RequestBody SendMessageDTO sendMessage) {
        return messageService.sendMessage(sendMessage);
    }

    @ApiOperation("设置消息状态")
    @PostMapping("/setRead/readFlag")
    public R setRead(@Validated @PathVariable("readFlag") int readFlag, @NotEmpty(message = "消息ID集合不允许为空") List<Long> messageIds) {
        messageService.setRead(messageIds, readFlag);
        return R.ok();
    }
}
