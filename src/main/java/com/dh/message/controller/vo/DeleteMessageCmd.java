package com.dh.message.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@ApiModel("删除待办")
@Data
public class DeleteMessageCmd {
    @ApiModelProperty("业务ID")
    @Length(max = 50)
    private String businessId;

    @ApiModelProperty("业务类型")
    @Length(max = 50)
    private String businessType;
}
