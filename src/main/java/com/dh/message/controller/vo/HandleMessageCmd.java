package com.dh.message.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

@ApiModel("置为已办")
@Data
public class HandleMessageCmd {

    @ApiModelProperty("业务ID")
    @Length(max = 50)
    private String businessId;

    @ApiModelProperty("业务类型")
    @Length(max = 50)
    private String businessType;

    @ApiModelProperty("消息接收人")
    @NotNull
    private Long receiveTo;
}
