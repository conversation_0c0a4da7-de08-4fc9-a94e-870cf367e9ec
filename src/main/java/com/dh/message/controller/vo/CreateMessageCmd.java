package com.dh.message.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel("创建待办")
@Data
public class CreateMessageCmd {

    @ApiModelProperty("主题")
    @Length(max = 100)
    @NotBlank
    private String subject;

    @ApiModelProperty("内容")
    @Length(max = 1000)
    @NotBlank
    private String content;

    @ApiModelProperty(value = "消息类型（1:待办，2:通知，3:公告）")
    @NotNull
    private Integer msgType;

    @ApiModelProperty("内容")
    @Length(max = 200)
    private String msgUrl;

    @ApiModelProperty("业务ID")
    @Length(max = 50)
    private String businessId;

    @ApiModelProperty("业务类型")
    @Length(max = 50)
    private String businessType;

    @ApiModelProperty("消息接收人")
    @NotNull
    private Long receiveTo;

    @ApiModelProperty("消息创建人")
    private Long createBy;

    @ApiModelProperty("消息参数")
    private String paramsValue;
}
