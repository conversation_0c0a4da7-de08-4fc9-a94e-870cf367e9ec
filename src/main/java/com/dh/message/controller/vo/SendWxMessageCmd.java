package com.dh.message.controller.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class SendWxMessageCmd {

    @NotNull(message = "推送微信消息的人数不能为空")
    @Size(min = 1, max = 1000, message = "推送微信消息的人数必须在 {min} - {max} 之间")
    private List<String> wxCpUserNames;

    @NotEmpty(message = "消息内容不能为空")
    private String content;
}
