package com.dh.message.controller;


import com.dh.common.util.R;
import com.dh.message.bean.fm.TaskMonitorFM;
import com.dh.message.bean.vo.TaskMonitorVO;
import com.dh.message.service.TaskMonitorService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 任务监控表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@RestController
@RequestMapping("/taskMonitor")
public class TaskMonitorController {

    @Resource
    private TaskMonitorService taskMonitorService;

    @PostMapping("/getTaskMonitorList")
    @ApiOperation("任务列表查询")
    public R<PageInfo<TaskMonitorVO>> getTaskMonitorList(@RequestBody TaskMonitorFM fm) {
        PageHelper.startPage(fm.getPageNum(), fm.getPageSize());
        return R.ok(new PageInfo<>(taskMonitorService.getTaskMonitorList(fm)));
    }


}

