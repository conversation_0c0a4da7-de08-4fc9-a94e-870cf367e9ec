//package com.dh.message.controller;
//
//import com.alibaba.fastjson.JSONObject;
//import com.dh.common.util.R;
//import com.dh.message.controller.vo.SendWxMessageCmd;
//import com.dh.message.entity.Contx;
//import com.dh.message.service.WxMessageService;
//import com.dh.message.service.weixin.WxCpClient;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiImplicitParam;
//import io.swagger.annotations.ApiImplicitParams;
//import io.swagger.annotations.ApiOperation;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.util.StringUtils;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
///**
// * @project: dh-message
// * @ClassName: WxMessageController
// * @author: Bin
// * @creat: 2021/6/7 14:14
// * 描述:企业微信发送信息
// */
//@Slf4j
//@RestController
//@RequestMapping("/WxMessage")
//@Api(tags = "发送企业微信信息")
//@AllArgsConstructor
//public class WxMessageController {
//
//    private  final WxMessageService wxMessageService;
//
//    private final WxCpClient wxCpClient;
//
//    //发送群聊
//    @GetMapping("/Robot_Message")
//    @ApiOperation(value = "发送群聊")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "content", value = "消息内容", dataType = "String", required = true)
//    })
//    public JSONObject Robot_Message(String content){
//        Contx contx =new Contx();
//        contx.setContent(content);
//        JSONObject PostObject= wxMessageService.Robot_To_Message(contx);
//        return PostObject;
//    }
//
//    //发送个人
//    @GetMapping("/To_push")
//    @ApiOperation(value = "发送个人")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "User", value = "用户，企业微信的账户名", dataType = "String", required = true),
//            @ApiImplicitParam(name = "Content", value = "消息内容", dataType = "String", required = true)
//    })
//    public void To_push(String User,String Content){
//        wxCpClient.pushToUser(User,Content);
//    }
//
//    @PostMapping("/pushToUsers")
//    public R<String> pushToUsers(@Validated @RequestBody SendWxMessageCmd cmd) {
//        wxCpClient.pushToUser(StringUtils.collectionToDelimitedString(cmd.getWxCpUserNames(), "|"), cmd.getContent());
//
//        return R.ok();
//    }
//}
