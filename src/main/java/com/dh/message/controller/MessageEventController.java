package com.dh.message.controller;

import com.dh.common.util.R;
import com.dh.message.controller.vo.CreateMessageCmd;
import com.dh.message.controller.vo.DeleteMessageCmd;
import com.dh.message.controller.vo.HandleMessageCmd;
import com.dh.message.convert.MessageConvert;
import com.dh.message.service.IMessageInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/event")
@Api(tags = "消息事件管理")
@AllArgsConstructor
@Validated
public class MessageEventController {

    private IMessageInfoService messageInfoService;

    private MessageConvert messageConvert;

    @ApiOperation("消息创建事件")
    @PostMapping("/create")
    public R<String> create(@RequestBody CreateMessageCmd cmd) {
        messageInfoService.save(messageConvert.toMessageInfo(cmd));

        return R.ok("");
    }

    @ApiOperation("消息已办事件")
    @PostMapping("/handle")
    public R<String> handle(@RequestBody HandleMessageCmd cmd) {
        messageInfoService.handle(cmd);

        return R.ok("");
    }

    @ApiOperation("消息删除事件")
    @PostMapping("/delete")
    public R<String> delete(@RequestBody DeleteMessageCmd cmd) {
        messageInfoService.delete(cmd);

        return R.ok("");
    }
}
