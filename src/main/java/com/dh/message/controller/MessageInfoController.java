package com.dh.message.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.message.constant.MessageConstant;
import com.dh.message.dto.MessageInfoDTO;
import com.dh.message.entity.MessageInfo;
import com.dh.message.entity.MessageTemplate;
import com.dh.message.service.DhMessageUrlService;
import com.dh.message.service.IMessageAliyunSmsTemplateService;
import com.dh.message.service.IMessageInfoService;
import com.dh.message.service.IMessageTemplateService;
import com.dh.message.util.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 消息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-09
 */
@RestController
@RequestMapping("/messageInfo")
@Api(tags = "消息中心接口")
@AllArgsConstructor
public class MessageInfoController {

    private IMessageInfoService messageInfoService;

    private IMessageAliyunSmsTemplateService messageAliyunSmsTemplateService;

    private IMessageTemplateService messageTemplateService;

    private DhMessageUrlService messageUrlService;

    @ApiOperation("保存消息")
    @PostMapping("/saveMessageInfo")
    public R saveMessageInfo(@RequestBody MessageInfoDTO messageInfo) {
        if (messageInfo == null) {
            return R.failed("消息不能为空!");
        }
        if (messageInfo.getMessageTemplateId() != null) {  //获取消息模板主题
            MessageTemplate messageTemplate = messageTemplateService.getById(messageInfo.getMessageTemplateId());
            int paramsNum = StringUtils.isEmpty(messageInfo.getMessageTemplateParam()) ? 0
                    : JSONObject.parseObject(messageInfo.getMessageTemplateParam()).size();
            if (messageTemplate == null) {
                return R.failed("未查询到对应的消息模板，请检查模板数据！");
            }

            if (messageTemplate.getParamsNum() != paramsNum) {
                return R.failed("消息模板参数个数不对，请检查模板！");
            }
            String subject = messageTemplateService.getSubjectByTemplate(messageInfo.getMessageTemplateId(),
                    messageInfo.getMessageTemplateParam());
            if ("ERROR".equals(subject)) {
                return R.failed("消息模板参数名称错误，请检查模板！");
            }
            messageInfo.setSubject(subject);
            messageInfo.setMsgType(messageTemplate.getMsgType());
        }
        if (messageInfo.getMsgType() == null) {
            return R.failed("消息类型不能为空!");
        }
        if (messageInfo.getMsgType().equals(MessageConstant.TASKS) && messageInfo.getBusinessId() == null) {
            return R.failed("业务主键id不能为空！");
        }
        if (messageInfo.getCreateBy() == null
                || "".equals(messageInfo.getCreateByName())) {
            return R.failed("消息推送人不能为空!");
        }
        messageInfo.setCreateDate(LocalDateTime.now());
        messageInfoService.saveMessageInfo(messageInfo);
        return R.ok("消息创建成功");
    }

    @ApiOperation("工作流消息回调接口")
    @PostMapping("/notifyWorkflowMessage/{businessId}/{businessType}/{receiveTo}")
    public R notifyWorkflowMessage(@PathVariable String businessId, @PathVariable String businessType,
                                   @PathVariable Long receiveTo) {
        if (businessId == null) {
            return R.failed("业务id不能为空!");
        }
        if (receiveTo == null) {
            return R.failed("接收人id不能为空!");
        }
        List<MessageInfo> messageInfoList = messageInfoService.getByBusinessIdAndType(businessId, businessType, receiveTo);
        if (CollectionUtils.isEmpty(messageInfoList)) {
            return R.failed("消息已处理或者不存在!");
        }
        for (MessageInfo messageInfo : messageInfoList) {
            messageInfo.setHandleFlag(1);
            messageInfo.setHandleDate(LocalDateTime.now());
        }
        messageInfoService.updateBatchById(messageInfoList);
        return R.ok("消息处理成功!");
    }

    @ApiOperation("消息回调接口")
    @PostMapping("/notifyMessage/{businessId}")
    public R notifyMessage(@PathVariable String businessId,
                           @RequestParam(required = false) String businessType,
                           @RequestParam(required = false) Long receiveTo) {
        if (businessId == null) {
            return R.failed("业务id不能为空");
        }
        List<MessageInfo> messageInfoList = messageInfoService.getByBusinessIdAndType(businessId, businessType, receiveTo);
        if (CollectionUtils.isEmpty(messageInfoList)) {
            return R.failed("消息不存在!");
        }
        for (MessageInfo messageInfo : messageInfoList) {
            messageInfo.setHandleFlag(1);
            messageInfo.setHandleDate(LocalDateTime.now());
        }
        messageInfoService.updateBatchById(messageInfoList);
        return R.ok("消息处理成功!");

    }

    @ApiOperation("消息回调接口")
    @PostMapping("/notifyMessage/{businessId}/{receiveTo}")
    public R notifyMessage(@PathVariable String businessId, @PathVariable String receiveTo) {
        if (businessId == null) {
            return R.failed("业务id不能为空");
        }
        List<MessageInfo> messageInfoList = messageInfoService.getByBusinessId(businessId, receiveTo);
        if (CollectionUtils.isEmpty(messageInfoList)) {
            return R.failed("消息已处理或者不存在!");
        }
        for (MessageInfo messageInfo : messageInfoList) {
            messageInfo.setHandleFlag(1);
            messageInfo.setHandleDate(LocalDateTime.now());
            messageInfoService.updateById(messageInfo);
        }
        return R.ok("消息处理成功!");

    }

    @ApiOperation("获取消息类型")
    @PostMapping("/findMessageType")
    public R findMessageType() {
        List<Map<String, Object>> typeList = new ArrayList();
        Map<String, Object> map = new HashMap<>();
        map.put("name", "我的待办");
        map.put("type", MessageConstant.TASKS);
        typeList.add(map);
        Map<String, Object> map4 = new HashMap<>();
        map4.put("name", "我的已办");
        map4.put("type", MessageConstant.HANDLE);
        typeList.add(map4);
        Map<String, Object> map2 = new HashMap<>();
        map2.put("name", "我的消息");
        map2.put("type", MessageConstant.NOTIFICATION);
        typeList.add(map2);
        return R.ok(typeList);
    }

    @ApiOperation("通过消息类型获取消息")
    @PostMapping("/findMessageByCondition/{messageType}/{pageIndex}/{pageSize}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "messageType", value = "消息类型",
                    required = true, paramType = "path", dataType = "Long"),
            @ApiImplicitParam(name = "pageIndex", value = "页码",
                    required = true, paramType = "path", dataType = "Long"),
            @ApiImplicitParam(name = "pageSize", value = "每页多少",
                    required = true, paramType = "path", dataType = "Long")
    })
    public R findMessageByCondition(@PathVariable Integer messageType, @PathVariable Long pageIndex,
                                    @PathVariable Long pageSize,
                                    @RequestParam(value = "subject", required = false) String subject) {
        if (messageType == null) {
            return R.failed("消息类型不能为空!");
        }
        Page<MessageInfo> page = new Page<>(pageIndex, pageSize);
        IPage<MessageInfo> messagePage = messageInfoService.findMessageByCondition(page, messageType, subject);
        return R.ok(messagePage);
    }

    @ApiOperation("标记为已读")
    @PostMapping("/isRead/{messageId}")
    public R isRead(@PathVariable Long messageId) {
        if (messageId == null) {
            return R.failed("消息不存在！");
        }
        MessageInfo messageInfo = messageInfoService.getById(messageId);
        if (messageUrlService.queryUrl(messageInfo.getMsgUrl())) {
            if (messageInfo.getReadFlag() != 1) {
                messageInfo.setReadFlag(1);
                messageInfoService.updateById(messageInfo);
            }
            if (messageInfo.getHandleFlag() != 1) {
                messageInfo.setHandleFlag(1);
                messageInfoService.updateById(messageInfo);
            }
        }

        return R.ok();
    }

    @ApiOperation("获取发送短信模板编码")
    @PostMapping("/findSmsTemplate")
    public R findSmsTemplate() {
        return R.ok(messageAliyunSmsTemplateService.list());
    }

    @ApiOperation("置为已办")
    @PostMapping("/setDone/{messageId}")
    public R setDone(@PathVariable Long messageId) {
        if (messageId == null) {
            return R.failed("消息不存在！");
        }
        MessageInfo messageInfo = messageInfoService.getById(messageId);
        if (messageInfo.getHandleFlag() != 1) {
            messageInfo.setHandleFlag(1);
            messageInfoService.updateById(messageInfo);
        }
        return R.ok();
    }
}
