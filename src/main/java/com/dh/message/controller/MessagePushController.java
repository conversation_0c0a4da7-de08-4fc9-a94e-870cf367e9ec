package com.dh.message.controller;

import com.dh.message.entity.MessagePush;
import com.dh.message.entity.MessagePushHis;
import com.dh.message.service.MessagePushHisService;
import com.dh.message.service.MessagePushService;
import com.dh.message.util.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * description
 *
 * <AUTHOR>
 * @since 2019/12/3
 */
@Slf4j
@RestController
@RequestMapping("/messagePush")
@Api(tags = "消息推送管理")
@AllArgsConstructor
public class MessagePushController {

    private MessagePushService messagePushServiceImpl;

    private MessagePushHisService messagePushHisServiceImpl;


    @ApiOperation(value = "添加消息")
    @RequestMapping(value = "/addMessagePush", method = RequestMethod.POST)
    public R<String> addMessagePush(@RequestBody MessagePush messagePush) {
        if (messagePush == null) {
            return R.failed("参数不允许为空");
        }
        messagePushServiceImpl.insertMessagePush(messagePush);
        return R.ok(messagePush.getId()).setMsg("success");
    }

    @ApiOperation(value = "查看消息推送状态 0 未推送 1推送成功 2 推送失败 -1 没有该消息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "messagePushId", value = "消息ID",
                    required = true, paramType = "path", dataType = "String"),
    })
    @RequestMapping(value = "/getMessagePushSendStatus", method = RequestMethod.POST)
    public R<Integer> getMessagePushSendStatus(@RequestBody String messagePushId) {
        try {
            MessagePush messagePush = messagePushServiceImpl.getMessagePush(messagePushId);
            if (messagePush != null) {
                return R.ok(messagePush.getSendStatus()).setMsg("success");
            } else {
                MessagePushHis messagePushHis = messagePushHisServiceImpl.getMessagePushHis(messagePushId);
                if (messagePushHis != null) {
                    return R.ok(messagePushHis.getSendStatus()).setMsg("success");
                } else {
                    return R.ok(-1).setMsg("success");
                }
            }
        } catch (Exception e) {
            log.error("addMessagePush exception.", e);
            return R.failed("exception");
        }
    }
}
