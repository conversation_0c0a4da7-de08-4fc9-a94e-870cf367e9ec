package com.dh.message.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * description
 *
 * <AUTHOR>
 * @since 2020/4/8
 */
@Data
public class SendAliyunSmsStatusDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "流水ID")
    private String bizId;

    @ApiModelProperty(value = "电话号码")
    private String phoneNumber;

    @ApiModelProperty(value = "发送时间")
    private String sendDate;
}
