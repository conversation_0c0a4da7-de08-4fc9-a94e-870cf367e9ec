package com.dh.message.dto;

import com.dh.message.bean.bo.AlarmLevelConfig;
import com.dh.message.bean.bo.NotifyTarget;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 扣减绩效DTO
 *
 * <AUTHOR>
 * @since 2025/8/6
 */
@Data
@Builder
public class DeductionPerformanceDTO {
    @ApiModelProperty(value = "监控任务ID")
    private Long id;

    @ApiModelProperty(value = "报警级别")
    private Integer level;

    @ApiModelProperty(value = "报警历史记录ID")
    private Long taskMonitorHistoryId;

    @ApiModelProperty(value = "监控任务信息")
    private TaskMonitorInfoDTO taskMonitorInfoDTO;

    @ApiModelProperty(value = "级别报警配置")
    private AlarmLevelConfig alarmLevelConfig;
}
