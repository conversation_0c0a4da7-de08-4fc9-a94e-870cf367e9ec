package com.dh.message.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2020/5/9
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "MessageChannelDTO对象")
public class MessageChannelDTO implements Serializable {

    @ApiModelProperty(value = "消息渠道（1 : PC 2 : APP 3 : 短信 4 : 邮件）")
    private Integer messageChannel;

    @ApiModelProperty(value = "渠道业务号码（例如：手机号，邮箱，微信openId）")
    private String channelNumber;

    @ApiModelProperty(value = "发送模板参数(多个参数用 , 隔开)")
    private String templateParam;

    @ApiModelProperty(value = "发送模板编码")
    private String templateCode;

}
