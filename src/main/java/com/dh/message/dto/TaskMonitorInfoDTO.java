package com.dh.message.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.dh.message.bean.bo.AlarmLevelConfig;
import com.dh.message.bean.bo.NotificationConfig;
import com.dh.message.bean.bo.RelatedNotificationConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 任务监控信息
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class TaskMonitorInfoDTO implements Serializable {
    @ApiModelProperty(value = "任务监控ID")
    private Long id;

    @ApiModelProperty(value = "任务监控配置ID")
    private Long tmsId;

    @ApiModelProperty(value = "业务ID")
    private Long businessId;

    @ApiModelProperty(value = "业务扩展ID")
    private Long businessExtId;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "任务开始时间")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "任务结束时间")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "任务扩展参数(放置一些业务数据, 方便模板渲染等操作)")
    private Map<String, Object> extParams;

    @ApiModelProperty(value = "任务状态", dataType = "TaskMonitorStatusEnum")
    private int status;

    @ApiModelProperty(value = "任务完成度")
    @TableField("rate")
    private Double rate;

    @ApiModelProperty(value = "直接负责人id 多个以,分割")
    private String relatedUid;

    @ApiModelProperty(value = "项目主管id 多个以,分割")
    private String projectManagerUid;

    @ApiModelProperty(value = "部门主管id")
    private Long deptManagerUid;

    @ApiModelProperty(value = "已报警标识, 采用位操作, 具体计算规则参考 AlertFlagConstant 类")
    private Integer alertFlag;

    @ApiModelProperty(value = "任务时间节点")
    private Map<String, LocalDateTime> dateNodes;

    @ApiModelProperty(value = "删除标识")
    private int delFlag;

    @ApiModelProperty(value = "任务监控类型 参考: TaskMonitorSettingTypeEnum 枚举")
    private Integer type;

    @ApiModelProperty(value = "报警分级配置列表")
    private List<AlarmLevelConfig> alarmLevels;

    @ApiModelProperty(value = "通知配置列表")
    private List<NotificationConfig> notifications;

    /**
     * 责任人通知配置
     */
    @ApiModelProperty(value = "责任人通知配置")
    private RelatedNotificationConfig relatedNotification;

    @ApiModelProperty(value = "任务内容")
    private String taskContent;

    @ApiModelProperty(value = "更新人")
    private Long updateBy;

    @ApiModelProperty(value = "任务报警配置标题")
    private String title;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateDate;

    @ApiModelProperty(value = "创建人")
    private Long createBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

}
