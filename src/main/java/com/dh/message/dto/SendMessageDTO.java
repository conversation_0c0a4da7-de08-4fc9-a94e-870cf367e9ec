package com.dh.message.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

@ApiModel(value = "发送消息对象")
@Data
public class SendMessageDTO {

    @ApiModelProperty("消息模板编码")
    @NotEmpty(message = "消息模板编码不允许为空")
    private String messageTemplateCode;

    @ApiModelProperty("消息模板参数")
    private Map<String, String> MessageTemplateParams;

    @ApiModelProperty("消息对应业务url")
    private String businessUrl;

    @ApiModelProperty("消息对应业务url参数")
    private Map<String, String> businessUrlParams;

    @ApiModelProperty("消息接收人")
    @NotEmpty(message = "消息接收人不允许为空")
    private List<Long> receiveToList;
}
