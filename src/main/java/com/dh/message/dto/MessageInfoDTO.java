package com.dh.message.dto;

import com.dh.message.entity.MessageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/5/9
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "MessageInfoDTO对象")
public class MessageInfoDTO extends MessageInfo {

    @ApiModelProperty(value = "消息渠道对象")
    private List<MessageChannelDTO> messageChannelList;

}
