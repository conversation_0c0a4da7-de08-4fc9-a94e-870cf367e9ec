package com.dh.message.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * description
 *
 * <AUTHOR>
 * @since 2020/4/8
 */
@Data
public class SendAliyunSmsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "电话号码")
    private String phoneNumbers;

    @ApiModelProperty(value = "参数")
    private AliyunSmsParamsDto paramsDto;

    @ApiModelProperty(value = "模板code")
    private String templateCode;

}
