package com.dh.message.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 触发报警
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Data
@Builder
public class TriggerAlertDTO {
    @ApiModelProperty(value = "触发级别")
    int level;

    @ApiModelProperty(value = "是否预警")
    Boolean isWarning;

    @ApiModelProperty(value = "报警标志")
    int alertFlag;

    @ApiModelProperty(value = "是否跳过通知")
    Boolean isSkip;

    @ApiModelProperty(value = "任务监控信息")
    TaskMonitorInfoDTO taskMonitorInfoDTO;
}
