package com.dh.message.dto;

import com.dh.message.enums.TaskMonitorHistoryTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 任务监控报警历史
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Data
@Builder
public class TaskMonitorAlertHistoryDTO {
    @ApiModelProperty(value = "任务监控ID")
    private Long tmId;

    @ApiModelProperty(value = "任务监控配置ID")
    private Long tmsId;

    @ApiModelProperty(value = "报警级别")
    private Integer level;

    @ApiModelProperty(value = "是否预警")
    private Boolean isWarning;

    @ApiModelProperty(value = "旧报警标识")
    private Integer oldAlertFlag;

    @ApiModelProperty(value = "新报警标识")
    private Integer alertFlag;

    @ApiModelProperty(value = "任务监控报警ID")
    private Long taskMonitorAlertId;

    @ApiModelProperty(value = "空日期报警(忽略通知)")
    private Boolean isEmptyDate;

    @ApiModelProperty(value = "新状态")
    private Integer status;

    @ApiModelProperty(value = "旧状态")
    private Integer oldStatus;

    /**
     * 导入到json
     *
     * @return
     */
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();

        map.put("level", level);
        map.put("isWarning", isWarning);
        map.put("oldAlertFlag", oldAlertFlag);
        map.put("alertFlag", alertFlag);
        map.put("isEmptyDate", isEmptyDate);
        map.put("taskMonitorAlertId", taskMonitorAlertId);
        if (status != null) {
            map.put("oldStatus", status);
            map.put("status", status);
        }

        return map;
    }

    public Integer getHistoryType() {
        return isWarning ? TaskMonitorHistoryTypeEnum.TriggerWarningType.getCode() : TaskMonitorHistoryTypeEnum.TriggerAlertType.getCode();
    }
}
