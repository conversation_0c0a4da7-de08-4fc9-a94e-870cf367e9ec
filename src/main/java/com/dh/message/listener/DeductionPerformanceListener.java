package com.dh.message.listener;

import com.dh.message.dto.DeductionPerformanceDTO;
import com.dh.message.listener.event.DeductionPerformanceEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;


import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static com.dh.message.service.impl.NotificationImpl.objectToMap;

/**
 * 绩效扣减事件侦听器
 *
 * <AUTHOR>
 * @since 2025/8/6
 */
@Component
@Slf4j
public class DeductionPerformanceListener {
    /**
     * 处理绩效扣减事件
     *
     * @param event 绩效扣减事件
     */
    @EventListener
    public void handleEvent(DeductionPerformanceEvent event) {
        DeductionPerformanceDTO deductionPerformanceDTO = event.getDeductionPerformanceDTO();

        log.info("绩效扣减事件 handleEvent {}", deductionPerformanceDTO);
        //deductionPerformanceDTO.get().forEach(notifyTarget -> {
        //    if (notifyTarget.getScore().compareTo(BigDecimal.valueOf(0)) > 0) { //扣减分数大于0
        //        Map<String, String> params = objectToMap(event.getDeductionPerformanceDTO().getTaskMonitorInfoDTO());
        //
        //        List<Long> uids = notifyTarget.getUids(params);
        //        log.info("绩效扣减事件 handleEvent {}", uids);
        //    }
        //});
    }
}
