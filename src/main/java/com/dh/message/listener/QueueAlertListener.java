package com.dh.message.listener;

import com.dh.dto.bean.dto.message.QueueAlertDTO;
import com.dh.message.config.RabbitMQConfig;
import com.dh.message.listener.event.QueueAlertEvent;
import com.dh.message.service.QueueMessageProduct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;


/**
 * 队列报警事件监听
 *
 * <AUTHOR>
 * @since 2025/8/2
 */
@Component
@Slf4j
public class QueueAlertListener {
    @Resource
    QueueMessageProduct<QueueAlertDTO> queueMessageHandler;

    @Resource
    RabbitMQConfig rabbitMQConfig;

    @EventListener
    public void handleEvent(QueueAlertEvent event) {
        Arrays.stream(event.getQueueAlertDTO().getQueues().split(",")).forEach(queue -> {
            pushChannel(queue, event.getQueueAlertDTO());
        });
    }

    private void pushChannel(String channel, QueueAlertDTO queueAlertDTO) {
        String queueName = channel + ".queue";
        String routingKey = channel + ".routing";
        rabbitMQConfig.dynamicBind(queueName, routingKey);

        queueMessageHandler.send(channel, queueAlertDTO);
    }
}
