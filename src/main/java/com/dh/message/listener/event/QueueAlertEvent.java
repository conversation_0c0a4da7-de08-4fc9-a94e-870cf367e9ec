package com.dh.message.listener.event;

import com.dh.dto.bean.dto.message.QueueAlertDTO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;


/**
 * 报警事件(触发队列)
 */
@EqualsAndHashCode(callSuper = true)
@Getter
public class QueueAlertEvent extends ApplicationEvent {
    private final QueueAlertDTO queueAlertDTO;

    public QueueAlertEvent(Object source, QueueAlertDTO queueAlertDTO) {
        super(source);
        this.queueAlertDTO = queueAlertDTO;
    }
}
