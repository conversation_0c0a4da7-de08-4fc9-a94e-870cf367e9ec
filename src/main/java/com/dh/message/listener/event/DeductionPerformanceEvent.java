package com.dh.message.listener.event;


import com.dh.message.dto.DeductionPerformanceDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 扣减绩效事件
 *
 * <AUTHOR>
 * @since 2025/8/6
 */
@EqualsAndHashCode(callSuper = true)
@Getter
public class DeductionPerformanceEvent  extends ApplicationEvent {
    private final DeductionPerformanceDTO deductionPerformanceDTO;

    public DeductionPerformanceEvent(Object source, DeductionPerformanceDTO deductionPerformanceDTO) {
        super(source);
        this.deductionPerformanceDTO = deductionPerformanceDTO;
    }
}
