package com.dh.message.listener.event;

import com.dh.message.dto.TriggerAlertDTO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 报警事件
 *
 * <AUTHOR>
 * @version 0.9.1
 * @since 2025/7/25
 */
@EqualsAndHashCode(callSuper = true)
@Getter
public class TaskMonitorAlertEvent extends ApplicationEvent {
    private final TriggerAlertDTO triggerAlertDTO;

    public TaskMonitorAlertEvent(Object source, TriggerAlertDTO triggerAlertDTO) {
        super(source);
        this.triggerAlertDTO = triggerAlertDTO;
    }
}
