package com.dh.message.task;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dh.message.config.SystemConfig;
import com.dh.message.constant.SystemConstant;
import com.dh.message.entity.MessagePush;
import com.dh.message.entity.MessagePushHis;
import com.dh.message.entity.MessagePushTaskRecord;
import com.dh.message.service.MessagePushHisService;
import com.dh.message.service.MessagePushService;
import com.dh.message.service.MessagePushTaskRecordService;
import com.dh.message.util.EntityConvert;
import com.dh.message.util.MessageSendUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 消息推送task -- 用于失败的消息重复推送
 */
@Slf4j
@Component
@EnableScheduling
public class MessagePushRepeatTask {

    @Autowired
    private MessagePushService messagePushServiceImpl;

    @Autowired
    private MessagePushHisService messagePushHisServiceImpl;

    @Autowired
    private MessagePushTaskRecordService messagePushTaskRecordServiceImpl;

    @Autowired
    private SystemConfig systemConfig;

    @Scheduled(cron = "0 */2 * * * ?")
    public void messagePushRepeat() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss SSS");
        LocalDateTime startTime = LocalDateTime.now();
        log.info("messagePushRepeat start: {}", startTime.format(formatter));
        MessagePushTaskRecord taskRecord = new MessagePushTaskRecord();
        taskRecord.setId(System.currentTimeMillis() + "");
        taskRecord.setStartTime(startTime);
        taskRecord.setType(2);

        log.error("max send num:::::{}", systemConfig.getMaxSendNum());
        //查询所有推送失败的消息
        List<MessagePush> messagePushList = messagePushServiceImpl.getMessagePushList(
                new QueryWrapper<MessagePush>()
                        .eq("status", SystemConstant.STATUS_VALID)
                        .eq("send_status", SystemConstant.SEND_STATUS_FAIL)
                        .lt("send_fail_times", systemConfig.getMaxSendNum()));
        //推送成功
        List<MessagePush> messagePushListSuccess = new ArrayList<>();
        List<String> messagePushIdListSuccess = new ArrayList<>();
        //推送失败
        List<MessagePush> messagePushListFail = new ArrayList<>();
        if (messagePushList != null && messagePushList.size() > 0) {
            taskRecord.setRecordNum(messagePushList.size());
            for (MessagePush messagePush : messagePushList) {
                //判断时间是否在允许范围内
                LocalDateTime lastSendFailTime = messagePush.getSendTime();
                lastSendFailTime = lastSendFailTime
                        .plusMinutes(systemConfig.getFailSendTime() * messagePush.getSendFailTimes());
                if (startTime.isAfter(lastSendFailTime)) {
                    //TODO 推送消息
                    boolean sendResult = MessageSendUtil.sendByAppMsg(messagePush);
                    if (sendResult) {
                        messagePush.setSendStatus(SystemConstant.SEND_STATUS_SUCCESS);
                        messagePush.setSendTime(LocalDateTime.now());
                        messagePushListSuccess.add(messagePush);
                        messagePushIdListSuccess.add(messagePush.getId());
                    } else {
                        messagePush.setSendStatus(SystemConstant.SEND_STATUS_FAIL);
                        messagePush.setSendFailMsg("test repeat fail" + ";;" + messagePush.getSendFailMsg());
                        messagePush.setSendTime(LocalDateTime.now());
                        messagePush.setSendFailTimes(messagePush.getSendFailTimes() + 1);
                        messagePushListFail.add(messagePush);
                    }
                } else {
                    log.info("[message no need send now,id]{}[sendFialTimes]{}[ lastSendFailTime]{}[nowTime]{}",
                            messagePush.getId(), messagePush.getSendFailTimes(),
                            messagePush.getSendTime().format(formatter),
                            startTime.format(formatter));
                }
            }
        } else {
            taskRecord.setRecordNum(0);
        }
        //推送成功的处理：1 归档到历史表 2 删除推送表纪录
        if (messagePushListSuccess.size() > 0) {
            List<MessagePushHis> list = EntityConvert.messagePushConvertMessagePushHis(messagePushListSuccess);
            messagePushHisServiceImpl.saveMessagePushHisList(list);
            messagePushServiceImpl.deleteMessagePush(messagePushIdListSuccess);
        }
        //推送失败处理 更新原纪录推送状态等信息
        if (messagePushListFail.size() > 0) {
            messagePushServiceImpl.updateMessagePushList(messagePushListFail);
        }
        taskRecord.setSuccessNum(messagePushListSuccess.size());
        taskRecord.setFailNum(messagePushListFail.size());
        LocalDateTime endTime = LocalDateTime.now();
        taskRecord.setEndTime(endTime);
        taskRecord.setCreateTime(endTime);
        messagePushTaskRecordServiceImpl.insertMessagePushTaskRecord(taskRecord);
        log.info("messagePushRepeat end:{} ", endTime.format(formatter));
    }
}
