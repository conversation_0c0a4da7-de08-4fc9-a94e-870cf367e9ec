package com.dh.message.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

/**
 * 心跳
 */
@Slf4j
@Component
@EnableScheduling
public class HeartbeatTask {

    //或直接指定时间间隔，例如：10秒
    //@Scheduled(fixedRate=10000)
   /* @Scheduled(cron = "0/10 * * * * ?")

   //pmd报未使用，暂时注释
    private void heartbeatTask() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss SSS");
        LocalDateTime now = LocalDateTime.now();
        log.info("HeartbeatTimer:{} ", now.format(formatter));
    }*/

}
