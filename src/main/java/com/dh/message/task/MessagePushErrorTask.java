package com.dh.message.task;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dh.message.config.SystemConfig;
import com.dh.message.constant.SystemConstant;
import com.dh.message.entity.MessagePush;
import com.dh.message.entity.MessagePushHis;
import com.dh.message.entity.MessagePushTaskRecord;
import com.dh.message.service.MessagePushHisService;
import com.dh.message.service.MessagePushService;
import com.dh.message.service.MessagePushTaskRecordService;
import com.dh.message.util.EntityConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 消息推送task -- 用于处理推送失败超过指定次数的消息
 */
@Slf4j
@Component
@EnableScheduling
public class MessagePushErrorTask {

    @Autowired
    private MessagePushService messagePushServiceImpl;

    @Autowired
    private MessagePushHisService messagePushHisServiceImpl;

    @Autowired
    private MessagePushTaskRecordService messagePushTaskRecordServiceImpl;


    @Autowired
    private SystemConfig systemConfig;

    @Scheduled(cron = "0 */5 * * * ?")
    public void messagePushError() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss SSS");
        LocalDateTime startTime = LocalDateTime.now();
        log.info("messagePushError start: {}", startTime.format(formatter));
        MessagePushTaskRecord taskRecord = new MessagePushTaskRecord();
        taskRecord.setId(System.currentTimeMillis() + "");
        taskRecord.setStartTime(startTime);
        taskRecord.setType(3);

        log.error("max send num:::::{}", systemConfig.getMaxSendNum());
        //查询所有推送失败超过指定次数 或者无效的消息
        List<MessagePush> messagePushList = messagePushServiceImpl.getMessagePushList(
                new QueryWrapper<MessagePush>()
                        //.eq("status", SystemConstant.STATUS_VALID)
                        .eq("send_status", SystemConstant.SEND_STATUS_FAIL)
                        .ge("send_fail_times", systemConfig.getMaxSendNum()));
        //推送成功的处理：1 归档到历史表 2 删除推送表纪录
        if (messagePushList != null && messagePushList.size() > 0) {
            taskRecord.setRecordNum(messagePushList.size());
            if (messagePushList.size() > 0) {
                List<MessagePushHis> list = EntityConvert.messagePushConvertMessagePushHis(messagePushList);
                messagePushHisServiceImpl.saveMessagePushHisList(list);

                List<String> messagePushIdList = new ArrayList<>();
                for (MessagePush messagePush : messagePushList) {
                    messagePushIdList.add(messagePush.getId());
                }
                messagePushServiceImpl.deleteMessagePush(messagePushIdList);
            }
        } else {
            taskRecord.setRecordNum(0);
        }
        taskRecord.setSuccessNum(0);
        taskRecord.setFailNum(0);
        LocalDateTime endTime = LocalDateTime.now();
        taskRecord.setEndTime(endTime);
        taskRecord.setCreateTime(endTime);
        messagePushTaskRecordServiceImpl.insertMessagePushTaskRecord(taskRecord);
        log.info("messagePushError end:{} ", endTime.format(formatter));
    }
}
