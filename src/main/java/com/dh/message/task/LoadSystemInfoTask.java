package com.dh.message.task;

import com.dh.message.entity.MessageAliyunSmsTemplate;
import com.dh.message.service.IMessageAliyunSmsTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 加载系统配置
 */
@Slf4j
@Component
@EnableScheduling
//@ConditionalOnProperty(value = "scheduledtask.execution", matchIfMissing = false)
public class LoadSystemInfoTask {

    @Autowired
    private IMessageAliyunSmsTemplateService messageAliyunSmsTemplateService;

    private static final Map<String, MessageAliyunSmsTemplate> ALIYUN_SMS_TEMPLATE_MAP = new HashMap<>();

    public static Map<String, MessageAliyunSmsTemplate> getMap() {
        return Collections.unmodifiableMap(ALIYUN_SMS_TEMPLATE_MAP);
    }

    @Scheduled(cron = "0/20 * * * * ?")
    public void loadAliyunSmsTemplateInfo() {
        List<MessageAliyunSmsTemplate> list = messageAliyunSmsTemplateService.listForLoad();
        if (!CollectionUtils.isEmpty(list)) {
            for (MessageAliyunSmsTemplate info : list) {
                if (info.getDelFlag() == 0) {
                    ALIYUN_SMS_TEMPLATE_MAP.put(info.getTemplateCode(), info);
                } else {
                    ALIYUN_SMS_TEMPLATE_MAP.remove(info.getTemplateCode());
                }
            }
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss SSS");
        LocalDateTime now = LocalDateTime.now();
        log.info(ALIYUN_SMS_TEMPLATE_MAP.toString());
        log.info("loadAliyunSmsTemplateInfo:[time]{} ", now.format(formatter));
    }
}
