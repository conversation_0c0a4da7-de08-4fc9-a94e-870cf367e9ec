package com.dh.message.task;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dh.message.constant.SystemConstant;
import com.dh.message.entity.MessagePush;
import com.dh.message.entity.MessagePushHis;
import com.dh.message.entity.MessagePushTaskRecord;
import com.dh.message.service.MessagePushHisService;
import com.dh.message.service.MessagePushService;
import com.dh.message.service.MessagePushTaskRecordService;
import com.dh.message.util.EntityConvert;
import com.dh.message.util.MessageSendUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 消息推送task
 */
@Slf4j
@Component
@EnableScheduling
public class MessagePushTask {

    @Autowired
    private MessagePushService messagePushServiceImpl;

    @Autowired
    private MessagePushHisService messagePushHisServiceImpl;

    @Autowired
    private MessagePushTaskRecordService messagePushTaskRecordServiceImpl;

    //pmd报未使用
   /* @Autowired
    private SystemConfig systemConfig;*/

    @Scheduled(cron = "0 */1 * * * ?")
    public void messagePushTask() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss SSS");
        LocalDateTime startTime = LocalDateTime.now();
        log.info("messagePushTask start: {}", startTime.format(formatter));

        MessagePushTaskRecord taskRecord = new MessagePushTaskRecord();
        taskRecord.setId(System.currentTimeMillis() + "");
        taskRecord.setStartTime(startTime);
        taskRecord.setType(1);

        //查询所有有效且未发送的消息
        List<MessagePush> messagePushList = messagePushServiceImpl.getMessagePushList(
                new QueryWrapper<MessagePush>()
                        .eq("status", SystemConstant.STATUS_VALID)
                        .eq("send_status", SystemConstant.SEND_STATUS_NOT));
        //推送成功
        List<MessagePush> messagePushListSuccess = new ArrayList<>();
        List<String> messagePushIdListSuccess = new ArrayList<>();
        //推送失败
        List<MessagePush> messagePushListFail = new ArrayList<>();
        if (messagePushList != null && messagePushList.size() > 0) {
            taskRecord.setRecordNum(messagePushList.size());
            for (MessagePush messagePush : messagePushList) {
                //TODO 推送消息
                boolean sendResult = MessageSendUtil.sendByAppMsg(messagePush);
                if (sendResult) {
                    messagePush.setSendStatus(SystemConstant.SEND_STATUS_SUCCESS);
                    messagePush.setSendTime(LocalDateTime.now());
                    messagePushListSuccess.add(messagePush);
                    messagePushIdListSuccess.add(messagePush.getId());
                } else {
                    messagePush.setSendStatus(SystemConstant.SEND_STATUS_FAIL);
                    messagePush.setSendFailMsg("test fail");
                    messagePush.setSendTime(LocalDateTime.now());
                    messagePush.setSendFailTimes(1);
                    messagePushListFail.add(messagePush);
                }
            }
        } else {
            taskRecord.setRecordNum(0);
        }
        //推送成功的处理：1 归档到历史表 2 删除推送表纪录
        if (messagePushListSuccess.size() > 0) {
            List<MessagePushHis> list = EntityConvert.messagePushConvertMessagePushHis(messagePushListSuccess);
            messagePushHisServiceImpl.saveMessagePushHisList(list);
            messagePushServiceImpl.deleteMessagePush(messagePushIdListSuccess);
        }
        //推送失败处理 更新原纪录推送状态等信息
        if (messagePushListFail.size() > 0) {
            messagePushServiceImpl.updateMessagePushList(messagePushListFail);
        }
        taskRecord.setSuccessNum(messagePushListSuccess.size());
        taskRecord.setFailNum(messagePushListFail.size());
        LocalDateTime endTime = LocalDateTime.now();
        taskRecord.setEndTime(endTime);
        taskRecord.setCreateTime(endTime);
        messagePushTaskRecordServiceImpl.insertMessagePushTaskRecord(taskRecord);
        log.info("messagePushTask end:{} ", endTime.format(formatter));
    }
}
