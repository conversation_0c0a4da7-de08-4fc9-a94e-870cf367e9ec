package com.dh.message.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dh.message.entity.MessageInfo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 消息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-09
 */
public interface MessageInfoMapper extends BaseMapper<MessageInfo> {

    IPage<MessageInfo> findMessageByCondition(Page page,
                                              @Param("messageType") Integer messageType,
                                              @Param("subject") String subject,
                                              @Param("userId") Long userId);
}
