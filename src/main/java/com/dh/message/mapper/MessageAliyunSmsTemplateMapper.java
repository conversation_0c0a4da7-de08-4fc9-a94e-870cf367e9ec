package com.dh.message.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dh.message.entity.MessageAliyunSmsTemplate;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 阿里云短信模版信息 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2020-05-14
 */
public interface MessageAliyunSmsTemplateMapper extends BaseMapper<MessageAliyunSmsTemplate> {

    @Select("select * from message_aliyun_sms_template")
    List<MessageAliyunSmsTemplate> listForLoad();
}
