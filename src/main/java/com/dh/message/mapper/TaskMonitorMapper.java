package com.dh.message.mapper;

import com.dh.dto.bean.dto.message.GetLastRunningTaskMonitorDTO;
import com.dh.dto.bean.dto.message.SubmitTaskMonitorResDTO;
import com.dh.dto.bean.dto.message.UpdateTaskMonitorDTO;
import com.dh.message.bean.entity.TaskMonitor;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dh.message.bean.fm.TaskMonitorFM;
import com.dh.message.bean.vo.TaskMonitorVO;
import com.dh.message.dto.TaskMonitorInfoDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 任务监控表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface TaskMonitorMapper extends BaseMapper<TaskMonitor> {
    List<TaskMonitorInfoDTO> getUnfinishedTask(@Param("status") Integer status, @Param("tmId") Long tmId);

    /**
     * 分批获取未完成的任务
     * @param pageSize 每批大小
     * @param offset 偏移量
     * @return 未完成的任务列表
     */
    List<TaskMonitorInfoDTO> getInProgressTaskByBatch(
                                                      @Param("pageSize") int pageSize,
                                                      @Param("offset") int offset);

    /**
     * 获取未完成任务的总数
     * @return 未完成任务总数
     */
    long getInProgressTaskCount();

    /**
     * 任务列表查询
     * @Author: zy
     * @Date: 2025/7/31 10:55
     * @param fm 查询条件
     * @return: java.util.List<com.dh.message.bean.vo.TaskMonitorVO>
     **/
    List<TaskMonitorVO> getTaskMonitorList(TaskMonitorFM fm);


    /**
     * 获取部门负责人id集合
     *
     * @param userIdList 用户Id集合
     * @Author: zgj
     * @Date: 2025/7/28 上午11:42
     * @return: java.util.List<java.lang.Long>
     **/
    List<Long> getDeptHeadId(@Param("userIdList") List<Long> userIdList);

    /**
     * 获取进行中的任务内容
     * @Author: zgj
     * @Date: 2025/8/2 上午10:41
     * @param updateTaskMonitorDTO 更新任务内容入参
     * @return: java.util.List<com.dh.message.dto.TaskMonitorInfoDTO>
     **/
    List<TaskMonitorInfoDTO> getRunningTaskMonitorList(UpdateTaskMonitorDTO updateTaskMonitorDTO);

    /**
     * 获取最近一次进行中的任务监控数据
     * @Author: zgj
     * @Date: 2023-06-08 20:45
     * @param dto 入参
     * @return: com.dh.common.util.R<com.dh.dto.bean.dto.message.SubmitTaskMonitorResDTO>
     **/
    SubmitTaskMonitorResDTO getLastRunningTaskMonitor(GetLastRunningTaskMonitorDTO dto);
}

