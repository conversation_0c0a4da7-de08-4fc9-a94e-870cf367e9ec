package com.dh.message.mapper;

import com.dh.message.bean.entity.TaskMonitorSetting;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dh.message.bean.fm.TaskMonitorSettingQueryFM;
import com.dh.message.bean.vo.TaskMonitorSettingVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 任务监控配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface TaskMonitorSettingMapper extends BaseMapper<TaskMonitorSetting> {

    /**
     * 查询任务监控配置列表
     * @Author: zy
     * @Date: 2025/7/25 11:07
     * @param id
     * @return: com.dh.message.bean.entity.TaskMonitorSetting
     **/
    List<TaskMonitorSetting> getTaskMonitorSettingList(TaskMonitorSettingQueryFM queryFM);

    /**
     * 根据id查询任务监控配置详情
     * @Author: zy
     * @Date: 2025/7/25 11:07
     * @param id
     * @return: com.dh.message.bean.entity.TaskMonitorSetting
     **/
    TaskMonitorSetting getTaskMonitorSettingDetail(Long id);

    /**
     * 获取所有的配置
     *
     * @param id id
     * @Author: zgj
     * @Date: 2025/7/29 上午9:52
     * @return: com.dh.message.bean.entity.TaskMonitorSetting
     **/
    TaskMonitorSetting getAllById(@Param("id") Long id);

    /**
     * 根据业务类型查询配置
     * @Author: zy
     * @Date: 2025/7/31 11:39
     * @param businessType
     * @return: com.dh.message.bean.vo.TaskMonitorSettingVO
     **/
    TaskMonitorSetting getTaskMonitorSettingByBusinessType(String businessType);

}
