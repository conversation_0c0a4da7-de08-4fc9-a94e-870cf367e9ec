<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dh-parent</artifactId>
        <groupId>com.dh</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>dh-message</artifactId>
    <packaging>${project.packaging}</packaging>
    <properties>
        <org.mapstruct.version>1.5.3.Final</org.mapstruct.version>
        <org.projectlombok.version>1.18.24</org.projectlombok.version>
        <!-- 打包类型，默认jar -->
        <project.packaging>jar</project.packaging>
        <profile.env/>
        <profile.swagger/>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
            <version>${org.projectlombok.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dh</groupId>
            <artifactId>dh-common</artifactId>
            <version>1.24-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.dh</groupId>
            <artifactId>dh-framework-security</artifactId>
            <version>2.9-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.dh</groupId>
            <artifactId>dh-dto</artifactId>
            <version>8.30-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!-- 解决Jackson2反序列化LocalDateTime报错 https://mvnrepository.com/artifact/com.fasterxml.jackson.datatype/jackson-datatype-jsr310 -->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>

        <!--阿里云短信-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.4.6</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
            <version>1.1.0</version>
        </dependency>
        <!--处理JSON格式-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>RELEASE</version>
        </dependency>
        <!--消息队列 -->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-activemq</artifactId>-->
<!--        </dependency>-->
<!--        &lt;!&ndash;消息队列连接池&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.apache.activemq</groupId>-->
<!--            <artifactId>activemq-pool</artifactId>-->
<!--        </dependency>-->

        <!-- mybatis plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <!-- mybatis数据库字段类型映射，此处是重点 -->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-typehandlers-jsr310</artifactId>
            <version>1.0.2</version>
        </dependency>
        <!-- mybatis-plus 代码生成，模板引擎 代码生成依赖包-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>2.0</version>
        </dependency>
        <!--分页-->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.2.13</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.jsqlparser</groupId>
                    <artifactId>jsqlparser</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <!-- connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- druid -->
        <!-- https://mvnrepository.com/artifact/com.alibaba/druid-spring-boot-starter -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.1.20</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/junit/junit -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>swagger-bootstrap-ui</artifactId>
            <version>1.9.6</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.jboss.logging</groupId>
            <artifactId>jboss-logging</artifactId>
        </dependency>

        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>
        <!-- 数据库加密 -->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>2.1.0</version>
        </dependency>

        <!-- 企业微信 -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-cp</artifactId>
            <version>4.4.0</version>
        </dependency>

                <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>
<!--         阿里云日志服务 -->
        <dependency>
            <groupId>com.dh</groupId>
            <artifactId>dh-log-starter</artifactId>
            <version>2.6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dh</groupId>
                    <artifactId>dh-dto</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 引入xxl-job -->
        <dependency>
            <groupId>com.dh</groupId>
            <artifactId>xxl-job-core-starter</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.dh</groupId>
            <artifactId>dh-framework-mq</artifactId>
            <version>1.4-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- 设置maven 打包jdk版本-->
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>

                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${org.projectlombok.version}</version>
                        </path>

                        <!-- additional annotation processor required as of Lombok 1.18.16 -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.3.1</version>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.6.2</version>
                <configuration>
                    <mainClass>com.dh.message.MessageApplication</mainClass>
                    <!-- 打包排除依赖 -->
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                        <exclude>
                            <groupId>com.baomidou</groupId>
                            <artifactId>mybatis-plus-generator</artifactId>
                        </exclude>
                        <exclude>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-starter-freemarker</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
                <!--修复没有主程序清单问题：https://www.cnblogs.com/niceboat/p/6230448.html-->
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--配置xls文件不被编码过滤-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>

            <!--spotbug-->
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <version>4.0.0</version>
                <dependencies>
                    <!-- overwrite dependency on spotbugs if you want to specify the version of spotbugs -->
                    <dependency>
                        <groupId>com.github.spotbugs</groupId>
                        <artifactId>spotbugs</artifactId>
                        <version>4.0.3</version>
                    </dependency>
                </dependencies>
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>validate</id>-->
<!--                        <phase>validate</phase>-->
<!--                        <goals>-->
<!--                            <goal>check</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
            </plugin>

            <!--PMD-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>3.13.0</version> <!-- or use version from pluginManagement -->
                <configuration>
                    <!-- failOnViolation is actually true by default, but can be disabled -->
                    <failOnViolation>true</failOnViolation>
                    <!-- printFailingErrors is pretty useful -->
                    <printFailingErrors>true</printFailingErrors>
                </configuration>
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>validate</id>-->
<!--                        <phase>validate</phase>-->
<!--                        <goals>-->
<!--                            <goal>check</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
            </plugin>

            <!--checkstyle-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.1.1</version>
                <configuration>
                    <configLocation>checkstyle.xml</configLocation>
                    <encoding>UTF-8</encoding>
                    <consoleOutput>true</consoleOutput>
                    <failsOnError>true</failsOnError>
                    <linkXRef>false</linkXRef>
                </configuration>
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>validate</id>-->
<!--                        <phase>validate</phase>-->
<!--                        <goals>-->
<!--                            <goal>check</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
            </plugin>
        </plugins>
        <!-- 资源文件过滤配置-->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>application.yml</include>
                    <include>application-${profile.env}.yml</include>                    <include>**/*.xml</include>
                    <include>**/*.xlsx</include>
                    <include>**/*.xls</include>
                    <include>**/*.properties</include>
                    <include>**/*.sql</include>
                    <include>templates/**</include>
                </includes>
            </resource>
        </resources>
    </build>

    <!-- 预定定义环境配置 -->
    <profiles>
        <profile>
            <id>war</id>
            <properties>
                <project.packaging>war</project.packaging>
            </properties>
            <dependencies>
                <!-- war包排除tomcat相关依赖，放置与tomcat容器冲突 -->
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <scope>provided</scope>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>encrypted</id>
            <build>
                <plugins>
                    <!-- jar包加密, classFinal插件必须在spring-boot-maven-plugin 的后面, 因为其插件的默认 phase 也是 package-->
                    <plugin>
                        <!-- https://gitee.com/roseboy/classfinal -->
                        <groupId>net.roseboy</groupId>
                        <artifactId>classfinal-maven-plugin</artifactId>
                        <version>1.2.1</version>
                        <configuration>
                            <password>000000</password><!--加密打包之后pom.xml会被删除，不用担心在jar包里找到此密码-->
                            <packages>com.dh.message</packages>
                            <cfgfiles>application.yml,application-dev.yml,application-qa.yml,application-prod.yml</cfgfiles>
                            <excludes>org.spring</excludes>
                        </configuration>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>classFinal</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>swagger</id>
            <properties>
                <profile.swagger>,swagger</profile.swagger>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <profile.env>dev</profile.env>
                <spring.profiles.active>${profile.env}${profile.swagger}</spring.profiles.active>
            </properties>
            <!-- 是否默认 true表示默认-->
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!-- 预发布环境 -->
        <profile>
            <id>qa</id>
            <properties>
                <profile.env>qa</profile.env>
                <spring.profiles.active>${profile.env}${profile.swagger}</spring.profiles.active>
            </properties>
        </profile>
        <!-- 生产环境 -->
        <profile>
            <id>prod</id>
            <properties>
                <profile.env>prod</profile.env>
                <spring.profiles.active>${profile.env}</spring.profiles.active>
            </properties>
        </profile>
        <!-- tp环境 -->
        <profile>
            <id>tp</id>
            <properties>
                <profile.env>tp</profile.env>
                <spring.profiles.active>${profile.env}</spring.profiles.active>
            </properties>
        </profile>
    </profiles>
</project>
